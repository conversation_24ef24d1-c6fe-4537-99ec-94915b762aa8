<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.ds.sjbs.DailyDataSubmitKssDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="statisticNum" resultType="com.rs.module.acp.entity.ds.sjbs.DailyDataSubmitKssDO">
        SELECT
            TO_CHAR(CURRENT_DATE - INTERVAL '1 day', 'YYYY-MM-DD') AS solidificationDate,
            org_code,
            org_code,
            COUNT(1) AS bjgry,
            COUNT(1) FILTER (WHERE xb = '1') AS bjgryMale,
            COUNT(1) FILTER (WHERE xb = '2') AS bjgryFemale,
            COUNT(1) FILTER (WHERE CURRENT_DATE - csrq::date &lt; 18*365) AS bjgryMinor,
            COUNT(1) FILTER (WHERE rsyy = '10') AS xzjl,
            COUNT(1) FILTER (WHERE rsyy = '11') AS sfjl,
            COUNT(1) FILTER (WHERE jgrybm IN (
            SELECT jgrybm FROM acp_db_health_check
            WHERE jszksfzc = '02' AND status = '03' AND is_del = 0
            )) AS jsyc,
            COUNT(1) FILTER (WHERE jgrybm IN (
            SELECT jgrybm FROM tem_talk_record
            WHERE is_del = 0 and end_time::date = CURRENT_DATE - INTERVAL '1 day'
            )) AS thjy,
            COUNT(1) FILTER (WHERE jgrybm IN (
            SELECT jgrybm FROM acp_gj_equipment_use
            WHERE is_del = 0 and status NOT IN ('01', '02', '09', '05')
            and actual_end_time::date = CURRENT_DATE - INTERVAL '1 day'
            )) AS jgsy,
            COUNT(1) FILTER(WHERE fxdj='1') AS yjzdfx,
            COUNT(1) FILTER(WHERE fxdj='2') AS ejzdfx,
            COUNT(1) FILTER(WHERE fxdj='3') AS sjzdfx,
            0 AS ddjsjd,
            COUNT(1) FILTER (WHERE jgrybm IN (
            SELECT jgrybm FROM acp_pm_prison_room_change
            WHERE is_del = 0 and state ='05'
            and room_change_time::date = CURRENT_DATE - INTERVAL '1 day'
            )) AS jstz,
            COUNT(1) FILTER (WHERE tssf is not null AND tssf !='99') AS tssf,
            COUNT(1) FILTER (WHERE gj not in ('156','158','344','446')) AS wjry,
            COUNT(1) FILTER (WHERE EXTRACT(YEAR FROM AGE(NOW(), csrq)) > 65) AS oldry,
            COUNT(1) FILTER (WHERE TO_CHAR(rssj, 'YYYY-MM-DD')::date = CURRENT_DATE - INTERVAL '1 day') AS mrrs,
            COUNT(1) FILTER (WHERE TO_CHAR(cssj, 'YYYY-MM-DD')::date = CURRENT_DATE - INTERVAL '1 day') AS mrcs
        FROM vw_acp_pm_prisoner_kss
        WHERE rssj &lt;= CURRENT_DATE - INTERVAL '1 day'
        AND (cssj IS NULL OR cssj &lt;= CURRENT_DATE - INTERVAL '0 day')
        <if test="orgCode != null">
            AND org_code = #{orgCode}
        </if>
        group by org_code
    </select>

    <delete id="deleteByCondition">
        DELETE FROM acp_ds_daily_data_submit_kss
        WHERE solidification_date = #{solidificationDate}
        <if test="orgCode != null and orgCode != ''">
            AND org_code =#{orgCode}
        </if>
    </delete>
</mapper>
