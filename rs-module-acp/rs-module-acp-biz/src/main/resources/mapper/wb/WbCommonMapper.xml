<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rs.module.acp.dao.wb.WbCommonDao">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getArraignmentRegistration" resultType="com.alibaba.fastjson.JSONObject">
        select * from(
        SELECT
            room_id
        FROM
            acp_wb_arraignment
        WHERE
            org_code = #{orgCode}
          AND status IN ( '2', '3', '99' )
          AND room_id IS NOT NULL
          AND is_del = 0
        union all
        SELECT
            room_id
        FROM
            acp_wb_bring_interrogation
        WHERE
            org_code = #{orgCode}
          AND status IN ( '2', '3', '99' )
          AND room_id IS NOT NULL
          AND is_del = 0) res
    </select>

    <select id="getLawyerMeetingById" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            apply_meeting_start_time,
            apply_meeting_end_time
        FROM
            acp_wb_lawyer_meeting
        where id = #{id}
          and is_del = 0
    </select>

    <select id="getConsularMeetingById" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            apply_meeting_start_time,
            apply_meeting_end_time
        FROM
            acp_wb_consular_meeting
        where id = #{id}
          and is_del = 0
    </select>

    <select id="getLawyerMeetingList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            room_id,
            apply_meeting_start_time,
            apply_meeting_end_time
        FROM
            acp_wb_lawyer_meeting
        where
                apply_meeting_end_time > TO_TIMESTAMP(#{applyMeetingStartTime}, 'YYYY-MM-DD HH24:MI')
          and status in ('2','3')
          and is_del = 0
    </select>

    <select id="getConsularMeetingList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            room_id,
            apply_meeting_start_time,
            apply_meeting_end_time
        FROM
            acp_wb_consular_meeting
        where
                apply_meeting_end_time > TO_TIMESTAMP(#{applyMeetingStartTime}, 'YYYY-MM-DD HH24:MI')
          and status in ('2','3')
          and is_del = 0
    </select>

    <select id="getFamilyRegistration" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            room_id
        FROM
            acp_wb_family_meeting
        WHERE
            org_code = #{orgCode}
          AND status IN ( '2', '3', '99' )
          AND room_id IS NOT NULL
          AND is_del = 0
    </select>

    <select id="getConsularTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            (select string_agg(DISTINCT COALESCE(work_unit,badwmc), '、')  from (select * from acp_wb_consular_meeting_person t2 where meeting.id = t2.consular_meeting_id and is_del = 0 order by rylx asc) person group by consular_meeting_id) meeting_unit,
            (select string_agg(DISTINCT xm, '、')  from (select * from acp_wb_consular_meeting_person t2 where meeting.id = t2.consular_meeting_id and is_del = 0 order by rylx asc) person group by consular_meeting_id) meeting_person,
            case
                when meeting_method = '0' then '现场会见'
                when meeting_method = '1' then '快速会见'
                when meeting_method = '2' then '远程会见'
                else ''
                end meeting_method,
            concat_ws('-',to_char(apply_meeting_start_time, 'YYYY-MM-DD HH24:MI'),to_char(apply_meeting_end_time, 'HH24:MI')) apply_meeting_time,
            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            to_char(check_in_time,'YYYY-MM-DD HH24:MI:SS') check_in_time,
            area_name,
            assignment_police,
            to_char(assignment_room_time,'YYYY-MM-DD HH24:MI:SS') assignment_room_time,
            escorting_time,
            escorting_police,
            to_char(escorting_operator_time,'YYYY-MM-DD HH24:MI:SS') escorting_operator_time,
            to_char(meeting_start_time,'YYYY-MM-DD HH24:MI:SS') meeting_start_time,
            to_char(inspection_time,'YYYY-MM-DD HH24:MI:SS') inspection_time,
            inspector,
            case
                when inspection_result = '0' then '正常'
                when inspection_result = '1' then '异常'
                else ''
                end inspection_result,
            to_char(meeting_end_time,'YYYY-MM-DD HH24:MI:SS') meeting_end_time,
            to_char(return_time,'YYYY-MM-DD HH24:MI:SS') return_time,
            return_police,
            to_char(return_inspection_time,'YYYY-MM-DD HH24:MI:SS') return_inspection_time,
            return_inspector,
            case
                when return_inspection_result = '0' then '正常'
                when return_inspection_result = '1' then '异常'
                else ''
                end return_inspection_result,
            to_char(return_operator_time,'YYYY-MM-DD HH24:MI:SS') return_operator_time,
            refuse_meet_reason AS refuse_reason,
            refuse_meet_materials AS refuse_materials,
            to_char(refuse_oper_time,'YYYY-MM-DD HH24:MI:SS') AS refuse_oper_time,
            refuse_operator,
            refuse_operator_sfzh
        FROM
            acp_wb_consular_meeting meeting
                left join acp_pm_area area on meeting.room_id = area.area_code
        where meeting.id = #{id}
    </select>

    <select id="getLawyerTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            concat_ws('、', lawyer1_name,lawyer2_name,lawyer3_name,lawyer4_name) meeting_person,
            concat_ws('、', lawyer1_practice_license_number,lawyer2_practice_license_number,lawyer3_practice_license_number,lawyer4_practice_license_number) lawyer_practice_license_number,
            case
                when meeting_method = '0' then '现场会见'
                when meeting_method = '1' then '快速会见'
                when meeting_method = '2' then '远程会见'
                else ''
                end meeting_method,
            concat_ws('-',to_char(apply_meeting_start_time, 'YYYY-MM-DD HH24:MI'),to_char(apply_meeting_end_time, 'HH24:MI')) apply_meeting_time,
            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            to_char(check_in_time,'YYYY-MM-DD HH24:MI:SS') check_in_time,
            area_name,
            assignment_police,
            to_char(assignment_room_time,'YYYY-MM-DD HH24:MI:SS') assignment_room_time,
            escorting_time,
            escorting_police,
            to_char(escorting_operator_time,'YYYY-MM-DD HH24:MI:SS') escorting_operator_time,
            to_char(meeting_start_time,'YYYY-MM-DD HH24:MI:SS') meeting_start_time,
            to_char(inspection_time,'YYYY-MM-DD HH24:MI:SS') inspection_time,
            inspector,
            case
                when inspection_result = '0' then '正常'
                when inspection_result = '1' then '异常'
                else ''
                end inspection_result,
            to_char(meeting_end_time,'YYYY-MM-DD HH24:MI:SS') meeting_end_time,
            to_char(return_time,'YYYY-MM-DD HH24:MI:SS') return_time,
            return_police,
            to_char(return_inspection_time,'YYYY-MM-DD HH24:MI:SS') return_inspection_time,
            return_inspector,
            case
                when return_inspection_result = '0' then '正常'
                when return_inspection_result = '1' then '异常'
                else ''
                end return_inspection_result,
            to_char(return_operator_time,'YYYY-MM-DD HH24:MI:SS') return_operator_time,
            refuse_meet_reason AS refuse_reason,
            refuse_meet_materials AS refuse_materials,
            to_char(refuse_oper_time,'YYYY-MM-DD HH24:MI:SS') AS refuse_oper_time,
            refuse_operator,
            refuse_operator_sfzh
        FROM
            acp_wb_lawyer_meeting meeting
                left join acp_pm_area area on meeting.room_id = area.area_code
        where meeting.id = #{id}
    </select>

    <select id="getFamilyMeetingTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            concat_ws('、', family_member1_name,family_member2_name,family_member3_name) meeting_person,
            concat_ws(',', family_member1_relationship,family_member2_relationship,family_member3_relationship) relationship,
            concat_ws('-',to_char(apply_meeting_start_time, 'YYYY-MM-DD HH24:MI'),to_char(apply_meeting_end_time, 'HH24:MI')) apply_meeting_time,
            (case when family_member1_id is not null then 1 else 0 end)+(case when family_member2_id is not null then 1 else 0 end)+(case when family_member3_id is not null then 1 else 0 end) meeting_number,

            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            to_char(check_in_time,'YYYY-MM-DD HH24:MI:SS') check_in_time,
            area_name,
            assignment_police,
            to_char(assignment_room_time,'YYYY-MM-DD HH24:MI:SS') assignment_room_time,
            escorting_time,
            escorting_police,
            to_char(escorting_operator_time,'YYYY-MM-DD HH24:MI:SS') escorting_operator_time,
            to_char(meeting_start_time,'YYYY-MM-DD HH24:MI:SS') meeting_start_time,
            to_char(inspection_time,'YYYY-MM-DD HH24:MI:SS') inspection_time,
            inspector,
            case
                when inspection_result = '0' then '正常'
                when inspection_result = '1' then '异常'
                else ''
                end inspection_result,
            to_char(meeting_end_time,'YYYY-MM-DD HH24:MI:SS') meeting_end_time,
            to_char(return_time,'YYYY-MM-DD HH24:MI:SS') return_time,
            return_police,
            to_char(return_inspection_time,'YYYY-MM-DD HH24:MI:SS') return_inspection_time,
            return_inspector,
            case
                when return_inspection_result = '0' then '正常'
                when return_inspection_result = '1' then '异常'
                else ''
                end return_inspection_result,
            to_char(return_operator_time,'YYYY-MM-DD HH24:MI:SS') return_operator_time,
            act_inst_id,
            task_id,
            refuse_meet_reason AS refuse_reason,
            refuse_meet_materials AS refuse_materials,
            to_char(refuse_oper_time,'YYYY-MM-DD HH24:MI:SS') AS refuse_oper_time,
            refuse_operator,
            refuse_operator_sfzh
        FROM
            acp_wb_family_meeting meeting
                left join acp_pm_area area on meeting.room_id = area.area_code
        where meeting.id = #{id}
    </select>

    <select id="getFamilyMeetingVideoTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            concat_ws('、', family_member1_name,family_member2_name,family_member3_name) meeting_person,
            concat_ws(',', family_member1_relationship,family_member2_relationship,family_member3_relationship) relationship,
            to_char(add_time, 'YYYY-MM-DD HH24:MI:SS') apply_meeting_time,

            (case when family_member1_id is not null then 1 else 0 end)+(case when family_member2_id is not null then 1 else 0 end)+(case when family_member3_id is not null then 1 else 0 end) meeting_number,
            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            to_char(notification_family_time,'YYYY-MM-DD HH24:MI:SS') notification_family_time,
            to_char(notification_meeting_date,'YYYY-MM-DD HH24:MI:SS') notification_meeting_date,
            notification_operator,
            to_char(notification_operator_time,'YYYY-MM-DD HH24:MI:SS') notification_operator_time,

            concat_ws('~', to_char(meeting_start_time,'YYYY-MM-DD HH24:MI'),to_char(meeting_end_time,'HH24:MI')),
            check_operator,
            to_char(check_operator_time,'YYYY-MM-DD HH24:MI:SS') check_operator_time,
            act_inst_id,
            task_id
        FROM
            acp_wb_family_meeting_video meeting
        where meeting.id = #{id}
    </select>

    <select id="verificationPersonnel" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        '提讯' businessname,
        to_char(start_apply_arraignment_time, 'HH24:MI:SS') applystarttime,
        case
        when CURRENT_TIMESTAMP &lt; start_apply_arraignment_time then '未开始'
        when CURRENT_TIMESTAMP > start_apply_arraignment_time and CURRENT_TIMESTAMP &lt; end_apply_arraignment_time then '提讯中'
        when CURRENT_TIMESTAMP > end_apply_arraignment_time and status = '4' then '已完结'
        else '提讯中'
        end statusname
        FROM
        acp_wb_arraignment
        where
        jgrybm = #{jgrybm}
        and to_char(start_apply_arraignment_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
        union all
        SELECT
        '提询' businessname,
        to_char(start_apply_arraignment_time, 'HH24:MI:SS') applystarttime,
        case
        when CURRENT_TIMESTAMP &lt; start_apply_arraignment_time then '未开始'
        when CURRENT_TIMESTAMP > start_apply_arraignment_time and CURRENT_TIMESTAMP &lt; end_apply_arraignment_time then '提询中'
        when CURRENT_TIMESTAMP > end_apply_arraignment_time and status = '4' then '已完结'
        else '提讯中'
        end statusname
        FROM
        acp_wb_bring_interrogation
        where
        jgrybm = #{jgrybm}
        and to_char(start_apply_arraignment_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
        union all
        SELECT
        '提解' businessname,
        to_char(apply_escort_date, 'YYYY-MM-DD') applystarttime,
        case
        when arraignment_start_time is not null and arraignment_end_time is not null then '已完结'
        when arraignment_start_time is null then '未开始'
        when arraignment_start_time is not null and arraignment_end_time is null then '提解中'
        else '提解中'
        end statusname
        FROM
        acp_wb_escort
        where
        jgrybm = #{jgrybm}
        and to_char(start_apply_arraignment_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
        union all
        SELECT
        '律师会见' businessname,
        to_char(apply_meeting_start_time, 'HH24:MI:SS') applystarttime,
        case
        when CURRENT_TIMESTAMP &lt; apply_meeting_start_time then '未开始'
        when CURRENT_TIMESTAMP > apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time then '会见中'
        when CURRENT_TIMESTAMP > apply_meeting_end_time and status = '4' then '已完结'
        else '会见中'
        end statusname
        FROM
        acp_wb_lawyer_meeting
        where
        jgrybm = #{jgrybm}
        and to_char(apply_meeting_start_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
        union all
        SELECT
        '家属会见' businessname,
        to_char(apply_meeting_start_time, 'HH24:MI:SS') applystarttime,
        case
        when CURRENT_TIMESTAMP &lt; apply_meeting_start_time then '未开始'
        when CURRENT_TIMESTAMP > apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time then '会见中'
        when CURRENT_TIMESTAMP > apply_meeting_end_time and status = '4' then '已完结'
        else '会见中'
        end statusname
        FROM
        acp_wb_family_meeting
        where
        jgrybm = #{jgrybm}
        and to_char(apply_meeting_start_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
        union all
        SELECT
        '家属单向视频会见' businessname,
        to_char(apply_meeting_start_time, 'HH24:MI:SS') applystarttime,
        case
        when CURRENT_TIMESTAMP &lt; apply_meeting_start_time then '未开始'
        when CURRENT_TIMESTAMP > apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time then '会见中'
        when CURRENT_TIMESTAMP > apply_meeting_end_time and status = '4' then '已完结'
        else '会见中'
        end statusname
        FROM
        acp_wb_family_meeting_video
        where
        jgrybm = #{jgrybm}
        and to_char(apply_meeting_start_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
        union all
        SELECT
        '领事hi见' businessname,
        to_char(apply_meeting_start_time, 'HH24:MI:SS') applystarttime,
        case
        when CURRENT_TIMESTAMP &lt; apply_meeting_start_time then '未开始'
        when CURRENT_TIMESTAMP > apply_meeting_start_time and CURRENT_TIMESTAMP &lt; apply_meeting_end_time then '会见中'
        when CURRENT_TIMESTAMP > apply_meeting_end_time and status = '4' then '已完结'
        else '会见中'
        end statusname
        FROM
        acp_wb_consular_meeting
        where
        jgrybm = #{jgrybm}
        and to_char(apply_meeting_start_time, 'YYYY-MM-DD') = to_char(CURRENT_DATE, 'YYYY-MM-DD')
        and is_del = 0
    </select>
    
    <select id="getHistoryArraignmentPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            tjjgmc meeting_unit,
            concat_ws('、',handler1_xm,handler2_xm,handler2_xm) meeting_person,
            arraignment_reason reason,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_arraignment
        where jgrybm = #{jgrybm}
        and is_del = 0
        order by add_time desc
    </select>

    <select id="getHistoryBringInterrogationPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            tjjgmc meeting_unit,
            concat_ws('、',handler1_xm,handler2_xm,handler2_xm) meeting_person,
            arraignment_reason reason,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_bring_interrogation
        where jgrybm = #{jgrybm}
          and is_del = 0
        order by add_time desc
    </select>

    <select id="getHistoryEscortPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            tjjgmc meeting_unit,
            concat_ws('、',handler1_xm,handler2_xm,handler2_xm) meeting_person,
            escort_reason reason,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_arraignment
        where jgrybm = #{jgrybm}
          and is_del = 0
        order by add_time desc
    </select>

    <select id="getHistoryFamilyMeetingPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            concat_ws('、',family_member1_name,family_member2_name,family_member2_name) meeting_person,
            concat_ws('、',family_member1_id_number,family_member2_id_number,family_member3_id_number) meeting_person_sfzh,
            concat_ws('、',family_member1_relationship,family_member2_relationship,family_member2_relationship) relationship,
            concat_ws('~',to_char(meeting_start_time,'YYYY-MM-DD HH24:MI'),to_char(meeting_end_time,'HH24:MI')) meeting_time,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_family_meeting
        where jgrybm = #{jgrybm}
          and is_del = 0
        order by add_time desc
    </select>

    <select id="getHistoryLawyerMeetingPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            concat_ws('、',lawyer1_name,lawyer2_name,lawyer3_name,lawyer4_name) meeting_person,
            concat_ws('~',to_char(meeting_start_time,'YYYY-MM-DD HH24:MI'),to_char(meeting_end_time,'HH24:MI')) meeting_time,
            case when meeting_method = '0' then '现场会见'
                 when meeting_method = '1' then '快速会见'
                 when meeting_method = '2' then '远程会见'
                 else '' end meeting_method,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_lawyer_meeting
        where jgrybm = #{jgrybm}
          and is_del = 0
        order by add_time desc
    </select>

    <select id="getHistoryConsularMeetingPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select id,
               (
                   SELECT
                       string_agg ( DISTINCT xm, '、' ) person_name
                   FROM
                       acp_wb_consular_meeting_person t2
                   WHERE
                       t1.ID = t2.consular_meeting_id
                     AND t2.is_del = 0
                   GROUP BY
                       consular_meeting_id
               ) meeting_person,
               concat_ws ( '~', to_char( meeting_start_time, 'YYYY-MM-DD HH24:MI' ), to_char( meeting_end_time, 'HH24:MI' ) ) meeting_time,
               CASE

                   WHEN meeting_method = '0' THEN
                       '现场会见'
                   WHEN meeting_method = '2' THEN
                       '远程会见' ELSE''
                   END meeting_method,
               to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        FROM
            acp_wb_consular_meeting t1
        WHERE
            jgrybm = #{ jgrybm }
          AND is_del = 0
        ORDER BY
            add_time DESC
    </select>

    <select id="getHistoryFamilyMeetingVideoPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            concat_ws('、',family_member1_name,family_member2_name,family_member2_name) meeting_person,
            concat_ws('、',family_member1_id_number,family_member2_id_number,family_member3_id_number) meeting_person_sfzh,
            concat_ws('、',family_member1_relationship,family_member2_relationship,family_member2_relationship) relationship,
            concat_ws('~',to_char(meeting_start_time,'YYYY-MM-DD HH24:MI'),to_char(meeting_end_time,'HH24:MI')) meeting_time,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_family_meeting_video
        where jgrybm = #{jgrybm}
          and is_del = 0
        order by add_time desc
    </select>

    <select id="getBringInterrogationTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            concat_ws('、', handler1_xm,handler2_xm,handler3_xm) meeting_person,
            tjjgmc meeting_unit,
            arraignment_reason reason,
            concat_ws('-',to_char(start_apply_arraignment_time, 'YYYY-MM-DD HH24:MI'),to_char(end_apply_arraignment_time, 'HH24:MI')) apply_meeting_time,
            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            escorting_time,
            escorting_police,
            to_char(escorting_operator_time,'YYYY-MM-DD HH24:MI:SS') escorting_operator_time,
            to_char(arraignment_start_time,'YYYY-MM-DD HH24:MI:SS') meeting_start_time,
            to_char(inspection_time,'YYYY-MM-DD HH24:MI:SS') inspection_time,
            inspector,
            case
                when inspection_result = '0' then '正常'
                when inspection_result = '1' then '异常'
                else ''
                end inspection_result,
            to_char(arraignment_end_time,'YYYY-MM-DD HH24:MI:SS') meeting_end_time,
            to_char(return_time,'YYYY-MM-DD HH24:MI:SS') return_time,
            return_police,
            to_char(return_inspection_time,'YYYY-MM-DD HH24:MI:SS') return_inspection_time,
            return_inspector,
            case
                when return_inspection_result = '0' then '正常'
                when return_inspection_result = '1' then '异常'
                else ''
                end return_inspection_result,
            to_char(return_operator_time,'YYYY-MM-DD HH24:MI:SS') return_operator_time,
            act_inst_id,
            task_id
        FROM
            acp_wb_bring_interrogation meeting
        where meeting.id = #{id}
    </select>

    <select id="getArraignmentTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            concat_ws('、', handler1_xm,handler2_xm,handler3_xm) meeting_person,
            tjjgmc meeting_unit,
            arraignment_reason reason,
            concat_ws('-',to_char(start_apply_arraignment_time, 'YYYY-MM-DD HH24:MI'),to_char(end_apply_arraignment_time, 'HH24:MI')) apply_meeting_time,
            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            escorting_time,
            escorting_police,
            to_char(escorting_operator_time,'YYYY-MM-DD HH24:MI:SS') escorting_operator_time,
            to_char(arraignment_start_time,'YYYY-MM-DD HH24:MI:SS') meeting_start_time,
            to_char(inspection_time,'YYYY-MM-DD HH24:MI:SS') inspection_time,
            inspector,
            case
                when inspection_result = '0' then '正常'
                when inspection_result = '1' then '异常'
                else ''
                end inspection_result,
            to_char(arraignment_end_time,'YYYY-MM-DD HH24:MI:SS') meeting_end_time,
            to_char(return_time,'YYYY-MM-DD HH24:MI:SS') return_time,
            return_police,
            to_char(return_inspection_time,'YYYY-MM-DD HH24:MI:SS') return_inspection_time,
            return_inspector,
            case
                when return_inspection_result = '0' then '正常'
                when return_inspection_result = '1' then '异常'
                else ''
                end return_inspection_result,
            to_char(return_operator_time,'YYYY-MM-DD HH24:MI:SS') return_operator_time
        FROM
            acp_wb_arraignment meeting
        where meeting.id = #{id}
    </select>

    <select id="getEscortTrajectory" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            concat_ws('、', handler1_xm,handler2_xm,handler3_xm) meeting_person,
            tjjgmc meeting_unit,
            escort_reason reason,
            to_char(apply_escort_date, 'YYYY-MM-DD') apply_meeting_time,
            to_char(meeting.add_time,'YYYY-MM-DD HH24:MI:SS') register_time,
            escorting_time,
            escorting_police,
            to_char(escorting_operator_time,'YYYY-MM-DD HH24:MI:SS') escorting_operator_time,
            to_char(arraignment_start_time,'YYYY-MM-DD HH24:MI:SS') meeting_start_time,
            to_char(inspection_time,'YYYY-MM-DD HH24:MI:SS') inspection_time,
            inspector,
            case
                when inspection_result = '0' then '正常'
                when inspection_result = '1' then '异常'
                else ''
                end inspection_result,
            to_char(arraignment_end_time,'YYYY-MM-DD HH24:MI:SS') meeting_end_time,
            to_char(return_time,'YYYY-MM-DD HH24:MI:SS') return_time,
            return_police,
            to_char(return_inspection_time,'YYYY-MM-DD HH24:MI:SS') return_inspection_time,
            return_inspector,
            case
                when return_inspection_result = '0' then '正常'
                when return_inspection_result = '1' then '异常'
                else ''
                end return_inspection_result,
            to_char(return_operator_time,'YYYY-MM-DD HH24:MI:SS') return_operator_time,
            act_inst_id,
            task_id
        FROM
            acp_wb_escort meeting
        where meeting.id = #{id}
    </select>

    <select id="getHistoryGoodsDeliveryPageByJgrybm" resultType="com.alibaba.fastjson.JSONObject">
        select
            id,
            sender_name meeting_person,
            id_number meeting_person_sfzh,
            relationship relationship,
            status,
            to_char(add_time,'YYYY-MM-DD HH24:MI:SS') node_create_time
        from acp_wb_goods_delivery
        where jgrybm = #{jgrybm}
          and is_del = 0
        order by add_time desc
    </select>

</mapper>
