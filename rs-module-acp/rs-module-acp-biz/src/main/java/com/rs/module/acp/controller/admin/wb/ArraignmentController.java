package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.wb.vo.ArraignmentRespVO;
import com.rs.module.acp.controller.admin.wb.vo.ArraignmentSaveReqVO;
import com.rs.module.acp.entity.wb.ArraignmentDO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.ArraignmentService;
import com.rs.module.acp.service.wb.WbBusinessApiService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.annotation.BusTrace;
import com.rs.module.base.enums.BusTypeEnum;
import com.rs.util.DicUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-提讯登记")
@RestController
@RequestMapping("/acp/wb/arraignment")
@Validated
public class ArraignmentController {

    @Resource
    private ArraignmentService arraignmentService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private WbBusinessApiService wbBusinessApiService;

    @GetMapping("/verificationPersonnel")
    @ApiOperation(value = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:arraignment:verificationPersonnel", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中",
            success = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中成功",
            fail = "实战平台-窗口业务-校验被监管人员是否存在其他业务正在进行中失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<JSONObject> verificationPersonnel(@RequestParam("jgrybm") String jgrybm) {
        return success(wbCommonService.verificationPersonnel(jgrybm,WbConstants.BUSINESS_TYPE_ARRAIGNMENT));
    }

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建提讯登记")
    @LogRecordAnnotation(bizModule = "acp:arraignment:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建提讯登记",
            success = "实战平台-窗口业务-创建提讯登记成功", fail = "实战平台-窗口业务-创建提讯登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createArraignment(@RequestBody ArraignmentSaveReqVO createReqVO) {
        return success(arraignmentService.createArraignment(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获取提讯登记详情（提讯人、业务登记信息、带出安检、会毕安检）")
    @ApiImplicitParam(name = "id", value = "业务ID")
    @LogRecordAnnotation(bizModule = "acp:arraignment:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获取提讯登记详情",
            success = "实战平台-窗口业务-获取提讯登记详情成功", fail = "实战平台-窗口业务-获取提讯登记详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<ArraignmentRespVO> getArraignment(@RequestParam("id") String id) {
        return success(arraignmentService.getArraignmentById(id));
    }

    @GetMapping("/getHistoryArraignment")
    @ApiOperation(value = "实战平台-窗口业务-获取历史提讯记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "dam:arraignment:getHistoryArraignment", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获取历史提讯记录",
            success = "实战平台-窗口业务-获取历史提讯记录成功", fail = "实战平台-窗口业务-获取历史提讯记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<ArraignmentRespVO>> getHistoryArraignment(@RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(arraignmentService.getHistoryArraignment(jgrybm,pageNo,pageSize));
    }

    @GetMapping("/signIn")
    @ApiOperation(value = "实战平台-窗口业务-提讯登记签到")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:signIn", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-提讯登记签到",
            success = "实战平台-窗口业务-提讯登记签到成功", fail = "实战平台-窗口业务-提讯登记签到失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> signIn(@RequestParam(value = "id", required = true) String id,
                                        @RequestParam(value = "checkInTime", required = true) String checkInTime) {
        if (arraignmentService.signIn(id, checkInTime)) {
            return success();
        }
        return error("签到失败");
    }

    @GetMapping("/getIdleInterrogationRoom")
    @ApiOperation(value = "实战平台-窗口业务-获取空闲审讯室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:getIdleInterrogationRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-获取空闲审讯室",
            success = "实战平台-窗口业务-获取空闲审讯室成功", fail = "实战平台-窗口业务-获取空闲审讯室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<List<JSONObject>> getIdleInterrogationRoom(@RequestParam(value = "id", required = true) String id) {
        return success(wbCommonService.getIdleInterrogationRoom(false));
    }

    @GetMapping("/allocationRoom")
    @ApiOperation(value = "实战平台-窗口业务-分配审讯室")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "roomId", value = "审讯室ID", required = true, dataType = "String"),
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:allocationRoom", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-分配审讯室",
            success = "实战平台-窗口业务-分配审讯室成功", fail = "实战平台-窗口业务-分配审讯室失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> allocationRoom(@RequestParam(value = "id", required = true) String id,
                                        @RequestParam(value = "roomId", required = true) String roomId) {
        if (arraignmentService.allocationRoom(id, roomId)) {
            return success();
        }
        return error("分配审讯室失败");
    }


    @PostMapping(path = "/escortingInspect")
    @ApiOperation(value = "实战平台-窗口业务-带出安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "arraignmentStartTime", value = "提讯开始时间", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:escortingInspect", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-带出安检",
            success = "实战平台-窗口业务-带出安检成功", fail = "实战平台-窗口业务-带出安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> escortingInspect(@RequestBody ArraignmentSaveReqVO updateReqVO) {
        if (arraignmentService.escortingInspect(updateReqVO)) {
            return success();
        }
        return error("保存带出安检失败");
    }

    @PostMapping("/returnInspection")
    @ApiOperation(value = "实战平台-窗口业务-会毕安检")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "arraignmentEndTime", value = "提讯结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:returnInspection", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-会毕安检",
            success = "实战平台-窗口业务-会毕安检成功", fail = "实战平台-窗口业务-会毕安检失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_TIXUN,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"经办时间\":\"{{#updateReqVO.addTime}}\"," +
            "\"办案单位\":\"{{#updateReqVO.tjjgmc}}\",\"提讯事由\":\"{{#updateReqVO.arraignmentReasonName}}\"," +
            "\"提讯开始时间\":\"{{#fDateTime(#updateReqVO.arraignmentStartTime)}}\",\"提讯结束时间\":\"{{#fDateTime(#updateReqVO.arraignmentEndTime)}}\"}")
    public CommonResult<Boolean> returnInspection(@RequestBody ArraignmentSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        ArraignmentDO arraignmentDO = arraignmentService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(arraignmentDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setTjjgmc(arraignmentDO.getTjjgmc());
        updateReqVO.setArraignmentReasonName(DicUtils.translate("ZD_WB_TXSY", arraignmentDO.getArraignmentReason()));
        updateReqVO.setJgrybm(arraignmentDO.getJgrybm());
        updateReqVO.setArraignmentStartTime(arraignmentDO.getArraignmentStartTime());
        /****记录轨迹 end****/

        if (arraignmentService.returnInspection(updateReqVO)) {
            return success();
        }
        return error("保存会毕安检失败");
    }

    @PostMapping("/additionalRecording")
    @ApiOperation(value = "实战平台-窗口业务-补录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "checkInTime", value = "签到时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "roomId", value = "审讯室ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPolice", value = "带出民警姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingPoliceSfzh", value = "带出民警身份证号", required = true, dataType = "String"),
            @ApiImplicitParam(name = "escortingTime", value = "带出监室时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectionResult", value = "检查结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "prohibitedItems", value = "违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "physicalExam", value = "体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "abnormalSituations", value = "异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "inspectionTime", value = "检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspectorSfzh", value = "检查人证件号码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "inspector", value = "检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "arraignmentStartTime", value = "提讯开始时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "arraignmentEndTime", value = "提讯结束时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectorSfzh", value = "会毕检查人身份证", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspector", value = "会毕检查人姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionTime", value = "会毕检查时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "returnInspectionResult", value = "会毕检查结果", dataType = "String"),
            @ApiImplicitParam(name = "returnProhibitedItems", value = "带回违禁物品登记", dataType = "String"),
            @ApiImplicitParam(name = "returnPhysicalExam", value = "带回体表检查登记", dataType = "String"),
            @ApiImplicitParam(name = "returnAbnormalSituations", value = "带回异常情况登记", dataType = "String"),
            @ApiImplicitParam(name = "returnTime", value = "带回监室时间", dataType = "String"),
            @ApiImplicitParam(name = "returnPolice", value = "带回民警姓名", dataType = "String"),
            @ApiImplicitParam(name = "returnPoliceSfzh", value = "带回民警身份证号", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:additionalRecording", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-补录",
            success = "实战平台-窗口业务-补录成功", fail = "实战平台-窗口业务-补录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    @BusTrace(busType = BusTypeEnum.YEWU_TIXUN,jgrybm="{{#updateReqVO.jgrybm}}", content = "{\"在押人员\":\"{{#updateReqVO.addTime}}\"," +
            "\"办案单位\":\"{{#updateReqVO.tjjgmc}}\",\"提讯事由\":\"{{#updateReqVO.arraignmentReasonName}}\"," +
            "\"提讯开始时间\":\"{{#fDateTime(#updateReqVO.arraignmentStartTime)}}\",\"提讯结束时间\":\"{{#fDateTime(#updateReqVO.arraignmentEndTime)}}\"}")
    public CommonResult<Boolean> additionalRecording(@RequestBody ArraignmentSaveReqVO updateReqVO) {
        /****记录轨迹 start****/
        ArraignmentDO arraignmentDO = arraignmentService.getById(updateReqVO.getId());
        updateReqVO.setAddTime(DateUtil.format(arraignmentDO.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        updateReqVO.setTjjgmc(arraignmentDO.getTjjgmc());
        updateReqVO.setArraignmentReasonName(DicUtils.translate("ZD_WB_TXSY", arraignmentDO.getArraignmentReason()));
        updateReqVO.setJgrybm(arraignmentDO.getJgrybm());
        /****记录轨迹 end****/

        if (arraignmentService.additionalRecording(updateReqVO)) {
            return success();
        }
        return error("保存补录失败");
    }

    @GetMapping("/getNewHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:arraignment:getNewHistoryMeetingByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录",
            success = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录成功",
            fail = "实战平台-窗口业务-（新版本）根据监管人员编码获取监管人员历史会见记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<JSONObject>> getNewHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                                     @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                                     @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(wbCommonService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize, WbConstants.BUSINESS_TYPE_ARRAIGNMENT));
    }

    @GetMapping("/getTrajectory")
    @ApiOperation(value = "实战平台-窗口业务-获得提讯会见轨迹")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:arraignment:getTrajectory", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得提讯会见轨迹",
            success = "实战平台-窗口业务-获得提讯会见轨迹成功", fail = "实战平台-窗口业务-获得提讯会见轨迹失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<List<JSONObject>> getTrajectory(@RequestParam("id") String id) {
        return success(wbCommonService.getTrajectory(id,WbConstants.BUSINESS_TYPE_ARRAIGNMENT));
    }

}
