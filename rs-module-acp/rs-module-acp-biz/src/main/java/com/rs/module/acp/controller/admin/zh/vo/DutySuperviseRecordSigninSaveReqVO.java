package com.rs.module.acp.controller.admin.zh.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导记录签至新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutySuperviseRecordSigninSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("值班记录ID")
    @NotEmpty(message = "值班记录ID不能为空")
    private String dutySuperviseRecordId;

    @ApiModelProperty("民警编号")
    @NotEmpty(message = "民警编号不能为空")
    private String policeId;

    @ApiModelProperty("民警姓名")
    @NotEmpty(message = "民警姓名不能为空")
    private String policeName;

}
