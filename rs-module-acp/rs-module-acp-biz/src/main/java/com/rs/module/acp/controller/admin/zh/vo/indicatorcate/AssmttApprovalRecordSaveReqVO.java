package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-考核审核记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttApprovalRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("加减分审核ID")
    private String assmttApprovalId;

    @ApiModelProperty("指标类型ID")
    //@NotEmpty(message = "指标类型ID不能为空")
    private String indicatorCateId;

    @ApiModelProperty("指标类型名称")
    //@NotEmpty(message = "指标类型名称不能为空")
    private String indicatorCateName;

    @ApiModelProperty("所属主指标ID")
    //@NotEmpty(message = "所属主指标ID不能为空")
    private String mainIndicatorId;

    @ApiModelProperty("所属子指标ID")
    @NotEmpty(message = "所属子指标ID不能为空")
    private String subIndicatorId;

    @ApiModelProperty("指标名称")
    //@NotEmpty(message = "指标名称不能为空")
    private String indicatorName;

    @ApiModelProperty("指标描述")
    //@NotEmpty(message = "指标描述不能为空")
    private String indicatorDescription;

    @ApiModelProperty("分值类型，字典：FIXED：固定分，RANGE：范围分")
    //@NotEmpty(message = "分值类型，字典：FIXED：固定分，RANGE：范围分不能为空")
    private String scoreType;

    @ApiModelProperty("加减分，字典：ADD：加分、SUBTRACT：扣分")
    private String addSubtract;

    @ApiModelProperty("基础分值")
    private BigDecimal baseScore;

    @ApiModelProperty("最小分值")
    private BigDecimal minScore;

    @ApiModelProperty("最大分值（仅范围分有效）")
    private BigDecimal maxScore;

    @ApiModelProperty("排序序号")
    //@NotNull(message = "排序序号不能为空")
    private Integer sortOrder;

    @ApiModelProperty("绩效原因")
    private String assmtReason;

    @ApiModelProperty("绩效分数")
    private BigDecimal assmtScore;

    @ApiModelProperty("综合审核原因")
    private String zhApprovalReason;

    @ApiModelProperty("综合审核分数")
    private BigDecimal zhApprovalScore;

    @ApiModelProperty("最终审核分数")
    private BigDecimal zzApprovalScore;


}
