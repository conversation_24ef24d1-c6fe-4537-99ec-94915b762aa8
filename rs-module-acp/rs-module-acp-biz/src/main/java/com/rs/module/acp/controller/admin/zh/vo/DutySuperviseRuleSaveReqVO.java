package com.rs.module.acp.controller.admin.zh.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导规则配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DutySuperviseRuleSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("排班模板ID")
    @NotEmpty(message = "排班模板ID不能为空")
    private String staffDutyTemplateId;

    @ApiModelProperty("值班岗位ID")
//    @NotEmpty(message = "值班岗位ID不能为空")
    private String staffDutyPostId;

    @ApiModelProperty("值班模板关联角色ID")
//    @NotEmpty(message = "值班模板关联角色ID不能为空")
    private String staffDutyRoleId;

    @ApiModelProperty("签到验证模式")
    @NotEmpty(message = "签到验证模式不能为空")
    private String signinValidMode;

    @ApiModelProperty("超时阀值（单位：分钟，必须为正整数）")
    @NotNull(message = "超时阀值不能为空")
    @Min(value = 1, message = "超时阀值必须大于等于1分钟")
    private Integer timeoutThreshold;

    @ApiModelProperty("超时推送对象")
    @NotEmpty(message = "超时推送对象不能为空")
    private String timeoutPushTarget;

    @ApiModelProperty("超时次数阀值（单位：次，必须为正整数）")
    @NotNull(message = "超时次数阀值不能为空")
    @Min(value = 1, message = "超时次数阀值必须大于等于1次")
    private Integer todayTimeoutCount;

    @ApiModelProperty("本日超时次数推送对象")
    @NotEmpty(message = "本日超时次数推送对象不能为空")
    private String todayTimeoutCountPushTarget;

    @ApiModelProperty("签到间隔（单位：分钟，必须为正整数）")
    @Min(value = 1, message = "签到间隔必须大于等于1分钟")
    private Integer signinInterval;

    @ApiModelProperty("签到时段开始")
//    @JsonFormat(pattern = "HH:mm")
    private String signinStartTime;

    @ApiModelProperty("签到时段结束")
//    @JsonFormat(pattern = "HH:mm")
    private String signinEndTime;

    @ApiModelProperty("补签时间（单位：分钟，必须为正整数）")
    @Min(value = 1, message = "补签时间必须大于等于1分钟")
    private Integer lateSigninTime;
    @ApiModelProperty("超时推送对象身份证号")
    private String timeoutPushTargetSfzh;

    @ApiModelProperty("本日超时次数推送对象身份证号")
    private String todayTimeoutCountPushTargetSfzh;


}
