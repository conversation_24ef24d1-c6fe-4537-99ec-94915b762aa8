package com.rs.module.acp.controller.admin.sys.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-语音播报-全局配置新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigGlobalSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("播报静音时段，格式：00:00:00-10:00:00")
    private String silentTimeSlots;

}
