package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("指标类型名称")
    private String typeName;
    @ApiModelProperty("初始化分值")
    private BigDecimal initScore;
    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;
    @ApiModelProperty("考核人姓名")
    private String assessorName;
    @ApiModelProperty("被考核对象类型，01:岗位、02：角色、03：用户")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_BKHDXLX")
    private String assessedObjectType;
    @ApiModelProperty("被考核对象ID")
    private String assessedObjectId;
    @ApiModelProperty("被考核对象名称")
    private String assessedObjectName;
    @ApiModelProperty("排序序号")
    private Integer sortOrder;
    @ApiModelProperty("备注")
    private String remark;
    private String cityName;

    private String cityCode;

    private String regName;

    private String regCode;

    private String orgName;

    private String orgCode;

    private Boolean isDel;

    private String addUser;

    private String addUserName;

    private Date addTime;

    private String updateUser;

    private String updateUserName;

    private Date updateTime;

    @ApiModelProperty("被考核对象")
    private List<IndicatorCateAssessedRespVO> assessedList;

    @ApiModelProperty("考核人")
    private List<IndicatorCateAssessorRespVO> assessorList;
}
