package com.rs.module.acp.controller.admin.zh.vo.shiftteam;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 值班模板班组人员信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffDutyTeamPersonRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("acp_zh_staff_duty_team.id")
    private String teamId;
    @ApiModelProperty("acp_zh_staff_duty_role.id")
    private String roleId;
    @ApiModelProperty("人员id")
    private String personId;
    @ApiModelProperty("人员姓名")
    private String personName;
    @ApiModelProperty("人员sfzh")
    private String personSfzh;
}
