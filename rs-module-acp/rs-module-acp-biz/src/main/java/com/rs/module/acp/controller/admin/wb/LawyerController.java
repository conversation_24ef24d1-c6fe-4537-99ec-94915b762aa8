package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.util.ObjectUtil;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.LawyerPageReqVO;
import com.rs.module.acp.controller.admin.wb.vo.LawyerRespVO;
import com.rs.module.acp.controller.admin.wb.vo.LawyerSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.LawyerSelectVO;
import com.rs.module.acp.entity.wb.LawyerDO;
import com.rs.module.acp.service.wb.LawyerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-律师信息管理")
@RestController
@RequestMapping("/acp/wb/lawyer")
@Validated
public class LawyerController {

    @Resource
    private LawyerService lawyerService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建律师")
    @LogRecordAnnotation(bizModule = "acp:lawyer:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建律师",
            success = "实战平台-窗口业务-创建律师成功", fail = "实战平台-窗口业务-创建律师失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createLawyer(@Valid @RequestBody LawyerSaveReqVO createReqVO) {
        return success(lawyerService.createLawyer(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-编辑律师")
    @LogRecordAnnotation(bizModule = "acp:lawyer:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-编辑律师",
            success = "实战平台-窗口业务-编辑律师成功", fail = "实战平台-窗口业务-编辑律师失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> updateLawyer(@Valid @RequestBody LawyerSaveReqVO updateReqVO) {
        lawyerService.updateLawyer(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除律师")
    @ApiImplicitParam(name = "ids", value = "业务ID")
    @LogRecordAnnotation(bizModule = "acp:lawyer:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除律师",
            success = "实战平台-窗口业务-删除律师成功", fail = "实战平台-窗口业务-删除律师失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteLawyer(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           lawyerService.deleteLawyer(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获取律师详情")
    @LogRecordAnnotation(bizModule = "acp:lawyer:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获取律师详情",
            success = "实战平台-窗口业务-获取律师详情成功", fail = "实战平台-窗口业务-获取律师详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    @ApiImplicitParam(name = "id", value = "业务ID")
    public CommonResult<LawyerRespVO> getLawyer(@RequestParam("id") String id) {
        LawyerDO lawyer = lawyerService.getLawyer(id);
        return success(BeanUtils.toBean(lawyer, LawyerRespVO.class));
    }

    @GetMapping("/getLawyerListByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-获取监管人可选择的律师")
    @LogRecordAnnotation(bizModule = "acp:lawyer:getLawyerListByJgrybm", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获取监管人可选择的律师",
            success = "实战平台-窗口业务-获取监管人可选择的律师成功", fail = "实战平台-窗口业务-获取监管人可选择的律师失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    @ApiImplicitParam(name = "jgrybm", value = "监管人员编号")
    public CommonResult<List<LawyerSelectVO>> getLawyerListByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        return success(lawyerService.getLawyerListByJgrybm(jgrybm));
    }

    @PostMapping("/getLawyerPageByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-分页获取监管人可选择的律师")
    @LogRecordAnnotation(bizModule = "acp:lawyer:checkJgryAssociation", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-分页获取监管人可选择的律师",
            success = "实战平台-窗口业务-分页获取监管人可选择的律师成功",
            fail = "实战平台-窗口业务-分页获取监管人可选择的律师失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<LawyerSelectVO>> getLawyerPageByJgrybm(@RequestBody LawyerPageReqVO pageReqVO) {
        if(ObjectUtil.isEmpty(pageReqVO.getJgrybm())){
            return error("被监管人员编码不可为空");
        }
        return success(lawyerService.getLawyerPageByJgrybm(pageReqVO));
    }
}
