package com.rs.module.acp.controller.admin.wb;

import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.wb.WbHomeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-首页工作台")
@RestController
@RequestMapping("/acp/wb/homeWorkbench")
@Validated
public class WbHomeController {

    @Autowired
    private WbHomeService wbHomeService;

    @GetMapping("/getToDayMeetingUpdates")
    @ApiOperation(value = "实战平台-窗口业务-获取今日会见动态")
    @LogRecordAnnotation(bizModule = "acp:homeWorkbench:getToDayMeetingUpdates", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-获取今日会见动态",success = "实战平台-窗口业务-获取今日会见动态成功",
            fail = "实战平台-窗口业务-获取今日会见动态失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<List<JSONObject>> getToDayMeetingUpdates() {
        return success(wbHomeService.getToDayMeetingUpdates());
    }

    @GetMapping("/getMeetingRoomStatus")
    @ApiOperation(value = "实战平台-窗口业务-获取会见室状态")
    @LogRecordAnnotation(bizModule = "acp:homeWorkbench:getMeetingRoomStatus", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-获取会见室状态", success = "实战平台-窗口业务-获取会见室状态成功",
            fail = "实战平台-窗口业务-获取会见室状态失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<JSONObject> getMeetingRoomStatus() {
        return success(wbHomeService.getMeetingRoomStatus());
    }

}
