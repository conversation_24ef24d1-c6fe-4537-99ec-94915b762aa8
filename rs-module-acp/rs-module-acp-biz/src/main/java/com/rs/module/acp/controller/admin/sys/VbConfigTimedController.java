package com.rs.module.acp.controller.admin.sys;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedRespVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigTimedSaveReqVO;
import com.rs.module.acp.entity.sys.VbConfigTimedDO;
import com.rs.module.acp.service.sys.VbConfigTimedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-语音播报-定时配置")
@RestController
@RequestMapping("/acp/sys/vbConfigTimed")
@Validated
public class VbConfigTimedController {

    @Resource
    private VbConfigTimedService vbConfigTimedService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-语音播报-定时配置")
    public CommonResult<String> createVbConfigTimed(@Valid @RequestBody VbConfigTimedSaveReqVO createReqVO) {
        return success(vbConfigTimedService.createVbConfigTimed(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-语音播报-定时配置")
    public CommonResult<Boolean> updateVbConfigTimed(@Valid @RequestBody VbConfigTimedSaveReqVO updateReqVO) {
        vbConfigTimedService.updateVbConfigTimed(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-语音播报-定时配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteVbConfigTimed(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           vbConfigTimedService.deleteVbConfigTimed(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-语音播报-定时配置")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<VbConfigTimedRespVO> getVbConfigTimed(@RequestParam("id") String id) {
        VbConfigTimedDO vbConfigTimed = vbConfigTimedService.getVbConfigTimed(id);
        return success(BeanUtils.toBean(vbConfigTimed, VbConfigTimedRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-语音播报-定时配置分页")
    public CommonResult<PageResult<VbConfigTimedRespVO>> getVbConfigTimedPage(@Valid @RequestBody VbConfigTimedPageReqVO pageReqVO) {
        PageResult<VbConfigTimedDO> pageResult = vbConfigTimedService.getVbConfigTimedPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VbConfigTimedRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-语音播报-定时配置列表")
    public CommonResult<List<VbConfigTimedRespVO>> getVbConfigTimedList(@Valid @RequestBody VbConfigTimedListReqVO listReqVO) {
        List<VbConfigTimedDO> list = vbConfigTimedService.getVbConfigTimedList(listReqVO);
        return success(BeanUtils.toBean(list, VbConfigTimedRespVO.class));
    }
}
