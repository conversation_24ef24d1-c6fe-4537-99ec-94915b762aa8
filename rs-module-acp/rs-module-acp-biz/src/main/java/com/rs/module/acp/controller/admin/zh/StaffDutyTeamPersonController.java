package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamPersonRespVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamPersonSaveReqVO;
import com.rs.module.acp.entity.zh.StaffDutyTeamPersonDO;
import com.rs.module.acp.service.zh.shiftteam.StaffDutyTeamPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班模板班组人员信息")
@RestController
@RequestMapping("/acp/zh/staffDutyTeamPerson")
@Validated
public class StaffDutyTeamPersonController {

    @Resource
    private StaffDutyTeamPersonService staffDutyTeamPersonService;

    @PostMapping("/create")
    @ApiOperation(value = "创建值班模板班组人员信息")
    public CommonResult<String> createStaffDutyTeamPerson(@Valid @RequestBody StaffDutyTeamPersonSaveReqVO createReqVO) {
        return success(staffDutyTeamPersonService.createStaffDutyTeamPerson(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新值班模板班组人员信息")
    public CommonResult<Boolean> updateStaffDutyTeamPerson(@Valid @RequestBody StaffDutyTeamPersonSaveReqVO updateReqVO) {
        staffDutyTeamPersonService.updateStaffDutyTeamPerson(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除值班模板班组人员信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteStaffDutyTeamPerson(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           staffDutyTeamPersonService.deleteStaffDutyTeamPerson(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得值班模板班组人员信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<StaffDutyTeamPersonRespVO> getStaffDutyTeamPerson(@RequestParam("id") String id) {
        StaffDutyTeamPersonDO staffDutyTeamPerson = staffDutyTeamPersonService.getStaffDutyTeamPerson(id);
        return success(BeanUtils.toBean(staffDutyTeamPerson, StaffDutyTeamPersonRespVO.class));
    }

    @PostMapping("/createOrUpdateBatch")
    @ApiOperation(value = "批量保存值班模板班组人员信息")
    public CommonResult<String> createOrUpdateBatch(@Valid @RequestBody List<StaffDutyTeamPersonSaveReqVO> list) {
        staffDutyTeamPersonService.createOrUpdateBatch(list);
        return success();
    }

}
