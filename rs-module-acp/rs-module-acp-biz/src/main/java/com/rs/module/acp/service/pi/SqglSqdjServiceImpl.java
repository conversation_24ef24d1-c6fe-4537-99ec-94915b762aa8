package com.rs.module.acp.service.pi;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bsp.common.orm.mybatis.service.BaseServiceImpl;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.fhs.trans.service.impl.TransService;
import com.rs.adapter.bsp.api.BspApi;
import com.rs.adapter.bsp.api.BspSdk;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.OrgUserRespDTO;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.config.WebSocketServer;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.controller.enums.SuoQingHeShiStatus;
import com.rs.module.acp.dao.pi.SqglSqdjDao;
import com.rs.module.acp.entity.pi.*;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.acp.service.wb.WbCommonService;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.pm.device.BaseDeviceService;
import com.rs.module.base.util.MsgUtil;
import com.rs.module.base.vo.RoomVideoVO;
import com.rs.util.DicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 实战平台-巡视管控-所情管理-所情登记 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SqglSqdjServiceImpl extends BaseServiceImpl<SqglSqdjDao, SqglSqdjDO> implements SqglSqdjService {

    @Value("${system-mark}")
    private String systemMark;

    @Resource
    private SqglSqdjDao sqglSqdjDao;

    @Autowired
    private SqglSqczService sqglSqczService;

    @Autowired
    private SqglSqdjGlryService sqglSqdjGlryService;

    @Autowired
    private SqglMbpzService sqglMbpzService;

    @Autowired
    private SqglSqdjtsdxService sqglSqdjtsdxService;

    @Autowired
    private SqglBjldszService sqglBjldszService;

    @Autowired
    private BspApi bspApi;

    @Autowired
    private UserApi userApi;

    @Autowired
    private AreaPrisonRoomService areaPrisonRoomService;

    @Autowired
    private WbCommonService wbCommonService;

    @Autowired
    private SqglSqczGywlService sqglSqczGywlService;

    @Autowired
    private PrisonerService prisonerService;

    @Autowired
    private BspSdk bspSdk;

    @Autowired
    private SqglGjlxpzService sqglGjlxpzService;

    @Autowired
    private BaseDeviceService baseDeviceService;

    @Autowired
    private AreaService areaService;
    @Autowired
    private BaseDeviceCameraService baseDeviceCameraService;

    @Autowired
    private TransService transService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSqglSqdj(SqglSqdjSaveReqVO createReqVO) {
        if (CollectionUtil.isEmpty(createReqVO.getTsdxList())) {
            throw new ServerException("推送对象列表不可为空");
        }
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        //这里是巡控岗在前端界面手动录入的所情，所以所情来源是 所情登记
        createReqVO.setEventSrc("001");

        // 插入
        SqglSqdjDO sqglSqdj = BeanUtils.toBean(createReqVO, SqglSqdjDO.class);
        sqglSqdj.setId(StringUtil.getGuid32());

        List<SqglSqdjGlryDO> sqglSqdjGlryDOList = getSqglSqdjGlryList(createReqVO, sqglSqdj.getId());

        if (CollectionUtil.isNotEmpty(sqglSqdjGlryDOList)) {
            sqglSqdjGlryService.saveBatch(sqglSqdjGlryDOList);
        }

        //创建巡控岗的处置
        SqglSqczDO xkSqcz = new SqglSqczDO();
        xkSqcz.setSqdjId(sqglSqdj.getId());
        xkSqcz.setHandleUserSfzh(sessionUser.getIdCard());
        xkSqcz.setHandleUserName(sessionUser.getName());
        xkSqcz.setHandleTime(new Date());
        xkSqcz.setHandleType("1");
        xkSqcz.setHandlePostCode("02");
        xkSqcz.setHandlePostName("巡控岗");
        xkSqcz.setHandleInfo(createReqVO.getHandleInfo());
        xkSqcz.setStatus("1");
        sqglSqczService.save(xkSqcz);

        //创建中间环节的处置
        createMiddleNode(createReqVO, sqglSqdj.getId());
        //更改状态--待处置
        sqglSqdj.setStatus("1");


        sqglSqdj.setHandleUserName(sessionUser.getName());
        //所情编码
        sqglSqdj.setEventCode(bspApi.executeByRuleCode("acp_prison_event_no", null));

        sqglSqdjDao.insert(sqglSqdj);
        // 返回
        return sqglSqdj.getId();
    }

    private String getPostName(String postCode) {
        List<String> postList = Arrays.asList(postCode.split(","));
        StringBuilder postCodeName = new StringBuilder();
        for (int i = 0; i < postList.size(); i++) {
            postCodeName.append(DicUtils.translate("ZD_POST", postList.get(i)));
            if (i < postList.size() - 1) {
                postCodeName.append(",");
            }
        }
        return postCodeName.toString();
    }

    /**
     * 创建中间环节
     *
     * @param createReqVO
     */
    private void createMiddleNode(SqglSqdjSaveReqVO createReqVO, String sqdjId) {
        Map<String, List<SqglSqdjtsdxSaveReqVO>> tsdxMap = new HashMap<>();
        for (SqglSqdjtsdxSaveReqVO txdx : createReqVO.getTsdxList()) {
            List<SqglSqdjtsdxSaveReqVO> saveReqVOList = new ArrayList<>();
            if (tsdxMap.containsKey(txdx.getPushPostCode())) {
                saveReqVOList = tsdxMap.get(txdx.getPushPostCode());
            }
            saveReqVOList.add(txdx);
            tsdxMap.put(txdx.getPushPostCode(), saveReqVOList);
        }
        //根据模板ID，查找所情模板配置
        Map<String, String> hanleGenericPlanMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(createReqVO.getEventTemplateId())) {
            SqglMbpzRespVO mbpzRespVO = sqglMbpzService.getSqglMbpzById(createReqVO.getEventTemplateId());
            if (ObjectUtil.isNotEmpty(mbpzRespVO)) {
                List<SqglMbpzTsdxRespVO> pushObjectSettingList = mbpzRespVO.getPushObjectSettingList();
                for (SqglMbpzTsdxRespVO tsdxRespVO : pushObjectSettingList) {
                    hanleGenericPlanMap.put(tsdxRespVO.getDisposePost(), tsdxRespVO.getDisposePlans());
                }
            }
        }

        LambdaQueryWrapper<SqglBjldszDO> bjldszDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bjldszDOLambdaQueryWrapper.eq(SqglBjldszDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .eq(SqglBjldszDO::getEventSrc, createReqVO.getEventSrc());

        SqglBjldszDO sqglBjldszDO = sqglBjldszService.getOne(bjldszDOLambdaQueryWrapper);

        List<SqglSqczDO> sqczDOList = new ArrayList<>();
        List<SqglSqdjtsdxDO> txdxList = new ArrayList<>();

        for (Map.Entry<String, List<SqglSqdjtsdxSaveReqVO>> map : tsdxMap.entrySet()) {
            SqglSqczDO sqglSqczDO = new SqglSqczDO();
            sqglSqczDO.setId(StringUtil.getGuid32());
            sqglSqczDO.setSqdjId(sqdjId);
            sqglSqczDO.setHandlePostCode(map.getKey());
            sqglSqczDO.setHandlePostName(DicUtils.translate("ZD_POST", map.getKey()));
            if (hanleGenericPlanMap.containsKey(map.getKey())) {
                sqglSqczDO.setHanleGenericPlan(hanleGenericPlanMap.get(map.getKey()));
            }
            sqglSqczDO.setHandleType("2");
            sqglSqczDO.setStatus("0");
            sqczDOList.add(sqglSqczDO);

            //创建推送信息
            if (CollectionUtil.isNotEmpty(map.getValue())) {
                for (SqglSqdjtsdxSaveReqVO sqglSqdjtsdxSaveReqVO : map.getValue()) {
                    SqglSqdjtsdxDO sqglSqdjtsdxDO = BeanUtils.toBean(sqglSqdjtsdxSaveReqVO, SqglSqdjtsdxDO.class);
                    sqglSqdjtsdxDO.setSqdjId(sqdjId);
                    sqglSqdjtsdxDO.setSqczId(sqglSqczDO.getId());
                    sqglSqdjtsdxDO.setPushTime(new Date());
                    txdxList.add(sqglSqdjtsdxDO);
                }
            }
        }
        sqglSqczService.saveBatch(sqczDOList);
        sqglSqdjtsdxService.saveBatch(txdxList);

        //发送websocket消息到页面
        if (ObjectUtil.isNotEmpty(sqglBjldszDO) && ObjectUtil.isNotEmpty(sqglBjldszDO.getOptionalLinkageSettings())
                && sqglBjldszDO.getOptionalLinkageSettings().contains("sqtc")) {
            JSONObject msg = new JSONObject();
            msg.put("businessType", "sqdj");
            msg.put("sqdjId", sqdjId);
            msg.put("eventLevel", createReqVO.getEventLevel());
            msg.put("happenTime", DateUtil.format(createReqVO.getEventStartTime(), "yyyy-MM-dd HH:mm:ss"));
            msg.put("eventDetails", createReqVO.getEventDetails());
            msg.put("url", String.format("/discipline/institutionSituation/inspectionOrCheck?id=%s&type=1", sqdjId));
            msg.put("status", "1");
            send(msg, txdxList);
        }

        //发送待办消息
        MsgAddVO msgAddVO = new MsgAddVO();
        msgAddVO.setBusinessId(sqdjId);
        msgAddVO.setMsgType("108_001");
        Map<String, Object> contentData = new HashMap<>();
        AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomService.getAreaPrisonRoom(createReqVO.getAreaId());
        if (ObjectUtil.isNotEmpty(areaPrisonRoomDO)) {
            contentData.put("areaName", areaPrisonRoomDO.getAreaName());
        }
        contentData.put("roomName", createReqVO.getAreaName());
        contentData.put("eventType", createReqVO.getEventType());
        StringBuilder bjgryXm = new StringBuilder();
        if (CollectionUtil.isNotEmpty(createReqVO.getJgryList())) {
            for (int i = 0; i < createReqVO.getJgryList().size(); i++) {
                bjgryXm.append(createReqVO.getJgryList().get(i).getPersonnelName());
                if (i < createReqVO.getJgryList().size() - 1) {
                    bjgryXm.append("、");
                }
            }
        }
        contentData.put("bjgryXm", bjgryXm.toString());
        msgAddVO.setContentData(contentData);
        msgAddVO.setModuleCode("ACP_JJKS_SQCZDB");
        msgAddVO.setUrl(String.format("/discipline/institutionSituation/inspectionOrCheck?id=%s&type=1", sqdjId));
        msgAddVO.setSpecify(true);

        for (Map.Entry<String, List<SqglSqdjtsdxSaveReqVO>> map : tsdxMap.entrySet()) {
            msgAddVO.setPcid(sqdjId + ":" + map.getKey());
            List<String> idCardlist = map.getValue().stream().map(SqglSqdjtsdxSaveReqVO::getPushUserSfzh).collect(Collectors.toList());
            List<OrgUserRespDTO> orgUserRespDTOList = bspApi.getUserByIdCards(String.join(",", idCardlist));
            List<ReceiveUser> receiveUserList = BeanUtils.toBean(orgUserRespDTOList, ReceiveUser.class);
            msgAddVO.setSpecifyReceiveUserList(receiveUserList);
            MsgUtil.sendMsg(msgAddVO);
        }
    }

    /**
     * 发送消息到页面
     *
     * @param msg
     * @param userList
     */
    private void send(JSONObject msg, List<SqglSqdjtsdxDO> userList) {
        for (SqglSqdjtsdxDO txdx : userList) {
            try {
                WebSocketServer.sendInfo(msg.toJSONString(), txdx.getPushUserSfzh());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private List<SqglSqdjGlryDO> getSqglSqdjGlryList(SqglSqdjSaveReqVO createReqVO, String sqdjId) {
        List<SqglSqdjGlryDO> sqglSqdjGlryDOList = new ArrayList<>();
        //在押人员
        if (CollectionUtil.isNotEmpty(createReqVO.getJgryList())) {
            for (SqglSqdjGlrySaveReqVO ry : createReqVO.getJgryList()) {
                SqglSqdjGlryDO sqglSqdjGlryDO = BeanUtils.toBean(ry, SqglSqdjGlryDO.class);
                sqglSqdjGlryDO.setPersonnelType("1");
                sqglSqdjGlryDO.setSqdjId(sqdjId);
                sqglSqdjGlryDO.setId(null);
                sqglSqdjGlryDOList.add(sqglSqdjGlryDO);
            }
        }
        //工作人员
        if (CollectionUtil.isNotEmpty(createReqVO.getGzryList())) {
            for (SqglSqdjGlrySaveReqVO ry : createReqVO.getGzryList()) {
                SqglSqdjGlryDO sqglSqdjGlryDO = BeanUtils.toBean(ry, SqglSqdjGlryDO.class);
                sqglSqdjGlryDO.setPersonnelType("2");
                sqglSqdjGlryDO.setSqdjId(sqdjId);
                sqglSqdjGlryDO.setId(null);
                sqglSqdjGlryDOList.add(sqglSqdjGlryDO);
            }
        }
        //工作人员
        if (CollectionUtil.isNotEmpty(createReqVO.getWlryList())) {
            for (SqglSqdjGlrySaveReqVO ry : createReqVO.getWlryList()) {
                SqglSqdjGlryDO sqglSqdjGlryDO = BeanUtils.toBean(ry, SqglSqdjGlryDO.class);
                sqglSqdjGlryDO.setPersonnelType("3");
                sqglSqdjGlryDO.setSqdjId(sqdjId);
                sqglSqdjGlryDO.setId(null);
                sqglSqdjGlryDOList.add(sqglSqdjGlryDO);
            }
        }
        //报警人员
        if (CollectionUtil.isNotEmpty(createReqVO.getBjryList())) {
            for (SqglSqdjGlrySaveReqVO ry : createReqVO.getBjryList()) {
                SqglSqdjGlryDO sqglSqdjGlryDO = BeanUtils.toBean(ry, SqglSqdjGlryDO.class);
                sqglSqdjGlryDO.setPersonnelType("4");
                sqglSqdjGlryDO.setSqdjId(sqdjId);
                sqglSqdjGlryDO.setId(null);
                sqglSqdjGlryDOList.add(sqglSqdjGlryDO);
            }
        }
        return sqglSqdjGlryDOList;
    }

    @Override
    public void updateSqglSqdj(SqglSqdjSaveReqVO updateReqVO) {
        // 校验存在
        validateSqglSqdjExists(updateReqVO.getId());
        // 更新
        SqglSqdjDO updateObj = BeanUtils.toBean(updateReqVO, SqglSqdjDO.class);
        sqglSqdjDao.updateById(updateObj);
    }

    @Override
    public void deleteSqglSqdj(String id) {
        // 校验存在
        validateSqglSqdjExists(id);
        // 删除
        sqglSqdjDao.deleteById(id);
    }

    private void validateSqglSqdjExists(String id) {
        if (sqglSqdjDao.selectById(id) == null) {
            throw new ServerException("实战平台-巡视管控-所情管理-所情登记数据不存在");
        }
    }

    @Override
    public SqglSqdjDO getSqglSqdj(String id) {
        return sqglSqdjDao.selectById(id);
    }

    @Override
    public PageResult<SqglSqdjDO> getSqglSqdjPage(SqglSqdjPageReqVO pageReqVO) {
        return sqglSqdjDao.selectPage(pageReqVO);
    }

    @Override
    public List<SqglSqdjDO> getSqglSqdjList(SqglSqdjListReqVO listReqVO) {
        return sqglSqdjDao.selectList(listReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean verify(SqglSqdjSaveReqVO updateReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SqglSqdjDO sqglSqdj = BeanUtils.toBean(updateReqVO, SqglSqdjDO.class);

        List<SqglSqdjGlryDO> sqglSqdjGlryDOList = getSqglSqdjGlryList(updateReqVO, sqglSqdj.getId());

        if (CollectionUtil.isNotEmpty(sqglSqdjGlryDOList)) {
            sqglSqdjGlryService.saveOrUpdateBatch(sqglSqdjGlryDOList);
        }

        LambdaUpdateWrapper<SqglSqczDO> sqglSqczDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sqglSqczDOLambdaUpdateWrapper.set(SqglSqczDO::getStatus, "1").set(SqglSqczDO::getHandleUserSfzh, sessionUser.getIdCard())
                .set(SqglSqczDO::getHandleUserName, sessionUser.getName()).set(SqglSqczDO::getHandleTime, new Date())
                .set(SqglSqczDO::getHandleInfo, updateReqVO.getHandleInfo());
        sqglSqczDOLambdaUpdateWrapper.eq(SqglSqczDO::getSqdjId, updateReqVO.getId())
                .eq(SqglSqczDO::getOrgCode, sessionUser.getOrgCode())
                .eq(SqglSqczDO::getHandleType, "1").eq(SqglSqczDO::getStatus, "0");
        sqglSqczService.update(sqglSqczDOLambdaUpdateWrapper);

        sqglSqdj.setHandleUserName(sessionUser.getName());

        //所情核实--办结的情况
        if ("2".equals(updateReqVO.getSaveType())) {
            sqglSqdj.setStatus("3");
            return updateById(sqglSqdj);
        }

        //创建中间环节的处置
        createMiddleNode(updateReqVO, sqglSqdj.getId());

        sqglSqdj.setStatus("1");
        sqglSqdjDao.updateById(sqglSqdj);
        return updateById(sqglSqdj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dispose(SqglSqczSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        SqglSqczDO sqglSqczDO = sqglSqczService.getById(updateReqVO.getId());
        sqglSqczDO.setHandleInfo(updateReqVO.getHandleInfo());
        sqglSqczDO.setHandleUserSfzh(sessionUser.getIdCard());
        sqglSqczDO.setHandleUserName(sessionUser.getName());
        sqglSqczDO.setHandleTime(new Date());
        sqglSqczDO.setStatus("1");
        if (ObjectUtil.isNotEmpty(updateReqVO.getAttUrl())) {
            sqglSqczDO.setAttUrl(wbCommonService.saveFile(null, updateReqVO.getAttUrl()));
        }

        boolean isLast = true;
        //判断是否是最后一个节点环节，如果是，则修改将所情登记的状态修改，并且创建最后一个节点
        LambdaQueryWrapper<SqglSqczDO> sqglSqczDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sqglSqczDOLambdaQueryWrapper.select(SqglSqczDO::getId, SqglSqczDO::getStatus).eq(SqglSqczDO::getSqdjId, updateReqVO.getSqdjId())
                .eq(SqglSqczDO::getHandleType, "2");
        List<SqglSqczDO> sqczDOList = sqglSqczService.list(sqglSqczDOLambdaQueryWrapper);
        for (SqglSqczDO sqczDO : sqczDOList) {
            if (sqglSqczDO.getId().equals(sqczDO.getId())) {
                continue;
            }
            if ("0".equals(sqczDO.getStatus())) {
                isLast = false;
                break;
            }
        }

        if ("03".equals(sqglSqczDO.getHandlePostCode()) && CollectionUtil.isNotEmpty(updateReqVO.getGlywList())) {
            List<SqglSqczGywlDO> gywlDOList = new ArrayList<>();
            for (SqglSqczGywlSaveReqVO gywlSaveReqVO : updateReqVO.getGlywList()) {
                SqglSqczGywlDO gywlDO = BeanUtils.toBean(gywlSaveReqVO, SqglSqczGywlDO.class);
                gywlDO.setSqczId(sqglSqczDO.getId());
                gywlDOList.add(gywlDO);
            }
            sqglSqczGywlService.saveOrUpdateBatch(gywlDOList);
        }


        //消除本岗位对应的待办
        SendMessageUtil.ProcessTodoMsg(updateReqVO.getSqdjId() + ":" + sqglSqczDO.getHandlePostCode(), sessionUser.getIdCard(), "pc");

        if (!isLast) {
            return sqglSqczService.updateById(sqglSqczDO);
        }

        //是中间环节最后一个处理的平行节点
        SqglSqdjDO sqglSqdjDO = getById(sqglSqczDO.getSqdjId());
        sqglSqdjDO.setStatus("2");
        //创建最后的所领导审批节点
        createApprove(sqglSqdjDO, sessionUser);
        sqglSqczService.updateById(sqglSqczDO);
        return updateById(sqglSqdjDO);
    }

    private void createApprove(SqglSqdjDO sqglSqdjDO, SessionUser sessionUser) {
        SqglSqczDO sqglSqczDO = new SqglSqczDO();
        sqglSqczDO.setId(StringUtil.getGuid32());
        sqglSqczDO.setSqdjId(sqglSqdjDO.getId());
        sqglSqczDO.setHandlePostCode("06");
        sqglSqczDO.setHandlePostName("所领导");
        sqglSqczDO.setHandleType("3");
        sqglSqczDO.setStatus("0");

        List<UserRespDTO> userList = userApi.getUserByOrgAndPost(sessionUser.getOrgCode(), "06");
        if (CollectionUtil.isEmpty(userList)) {
            throw new ServerException("所领导岗位下未配置用户，请配置用户后再提交");
        }
        List<SqglSqdjtsdxDO> tsdxList = new ArrayList<>();
        for (UserRespDTO userRespDTO : userList) {
            SqglSqdjtsdxDO sqdjtsdxDO = new SqglSqdjtsdxDO();
            sqdjtsdxDO.setSqdjId(sqglSqdjDO.getId());
            sqdjtsdxDO.setSqczId(sqglSqczDO.getId());
            sqdjtsdxDO.setPushPostCode("06");
            sqdjtsdxDO.setPushPostName("所领导");
            sqdjtsdxDO.setPushUserSfzh(userRespDTO.getIdCard());
            sqdjtsdxDO.setPushUserName(userRespDTO.getName());
            sqdjtsdxDO.setPushTime(new Date());
            tsdxList.add(sqdjtsdxDO);
        }

        sqglSqczService.save(sqglSqczDO);
        sqglSqdjtsdxService.saveBatch(tsdxList);

        LambdaQueryWrapper<SqglBjldszDO> bjldszDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bjldszDOLambdaQueryWrapper.eq(SqglBjldszDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .eq(SqglBjldszDO::getEventSrc, sqglSqdjDO.getEventSrc());

        SqglBjldszDO sqglBjldszDO = sqglBjldszService.getOne(bjldszDOLambdaQueryWrapper);

        //发送websocket消息到页面
        if (ObjectUtil.isNotEmpty(sqglBjldszDO) && ObjectUtil.isNotEmpty(sqglBjldszDO.getOptionalLinkageSettings())
                && sqglBjldszDO.getOptionalLinkageSettings().contains("sqtc")) {
            JSONObject msg = new JSONObject();
            msg.put("businessType", "sqdj");
            msg.put("sqdjId", sqglSqdjDO.getId());
            msg.put("eventLevel", sqglSqdjDO.getEventLevel());
            msg.put("happenTime", DateUtil.format(sqglSqdjDO.getEventStartTime(), "yyyy-MM-dd HH:mm:ss"));
            msg.put("eventDetails", sqglSqdjDO.getEventDetails());
            msg.put("url", String.format("/discipline/institutionSituation/inspectionOrCheck?id=%s&type=1", sqglSqdjDO.getId()));
            msg.put("status", "2");
            send(msg, tsdxList);
        }

        //发送待办消息
        MsgAddVO msgAddVO = new MsgAddVO();
        msgAddVO.setBusinessId(sqglSqczDO.getSqdjId());
        msgAddVO.setMsgType("108_001");
        Map<String, Object> contentData = new HashMap<>();
        AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomService.getAreaPrisonRoom(sqglSqdjDO.getAreaId());
        if (ObjectUtil.isNotEmpty(areaPrisonRoomDO)) {
            contentData.put("areaName", areaPrisonRoomDO.getAreaName());
        }
        contentData.put("roomName", sqglSqdjDO.getAreaName());
        contentData.put("eventType", sqglSqdjDO.getEventType());

        LambdaQueryWrapper<SqglSqdjGlryDO> glryDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        glryDOLambdaQueryWrapper.select(SqglSqdjGlryDO::getPersonnelName).eq(SqglSqdjGlryDO::getSqdjId, sqglSqdjDO.getId())
                .eq(SqglSqdjGlryDO::getPersonnelType, "1");
        List<SqglSqdjGlryDO> jgryList = sqglSqdjGlryService.list(glryDOLambdaQueryWrapper);

        StringBuilder bjgryXm = new StringBuilder();
        if (CollectionUtil.isNotEmpty(jgryList)) {
            for (int i = 0; i < jgryList.size(); i++) {
                bjgryXm.append(jgryList.get(i).getPersonnelName());
                if (i < jgryList.size() - 1) {
                    bjgryXm.append("、");
                }
            }
        }
        contentData.put("bjgryXm", bjgryXm.toString());
        msgAddVO.setContentData(contentData);
        msgAddVO.setModuleCode("ACP_JJKS_SQSHDB");
        msgAddVO.setUrl(String.format("/discipline/institutionSituation/inspectionOrCheck?id=%s&type=2", sqglSqdjDO.getId()));
        msgAddVO.setSpecify(true);
        msgAddVO.setPcid(sqglSqdjDO.getId() + ":" + sqglSqczDO.getHandlePostCode());
        msgAddVO.setSpecifyReceiveUserList(BeanUtils.toBean(userList, ReceiveUser.class));
        MsgUtil.sendMsg(msgAddVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approve(SqglSqczApproveSaveReqVO updateReqVO) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        SqglSqczDO sqglSqczDO = sqglSqczService.getById(updateReqVO.getId());
        sqglSqczDO.setHandleUserSfzh(sessionUser.getIdCard());
        sqglSqczDO.setHandleUserName(sessionUser.getName());
        sqglSqczDO.setHandleTime(new Date());
        sqglSqczDO.setApproveInfo(JSON.toJSONString(updateReqVO.getApproveInfoList()));

        boolean isPass = true;

        List<String> noPassIdList = new ArrayList<>();

        for (SqglSqczApproveChildSaveReqVO childSaveReqVO : updateReqVO.getApproveInfoList()) {
            if ("0".equals(childSaveReqVO.getApprovalResult())) {
                isPass = false;
                //对应的业务需要重新提交
                noPassIdList.add(childSaveReqVO.getSqczId());
            }
        }

        //消除本岗位对应的待办
        SendMessageUtil.ProcessTodoMsg(updateReqVO.getSqdjId() + ":" + sqglSqczDO.getHandlePostCode(), sessionUser.getIdCard(), "pc");

        SqglSqdjDO sqglSqdjDO = getById(sqglSqczDO.getSqdjId());

        if (isPass) {
            sqglSqczDO.setStatus("3");
            sqglSqdjDO.setStatus("3");
            sqglSqczService.updateById(sqglSqczDO);
            return updateById(sqglSqdjDO);
        }

        //审核不通过，需要重新处理
        LambdaQueryWrapper<SqglSqczDO> sqglSqczDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sqglSqczDOLambdaQueryWrapper.in(SqglSqczDO::getId, noPassIdList);

        List<SqglSqczDO> oldSqczList = sqglSqczService.list(sqglSqczDOLambdaQueryWrapper);

        List<SqglSqczDO> addSqczList = new ArrayList<>();
        List<SqglSqdjtsdxDO> tsdxList = new ArrayList<>();

        Map<String, List<SqglSqdjtsdxDO>> tsdxMap = new HashMap<>();

        for (SqglSqczDO sqczDO : oldSqczList) {
            SqglSqczDO addSqcz = new SqglSqczDO();
            addSqcz.setId(StringUtil.getGuid32());
            addSqcz.setSqdjId(sqglSqdjDO.getId());
            addSqcz.setHandlePostCode(sqczDO.getHandlePostCode());
            addSqcz.setHandlePostName(sqczDO.getHandlePostName());
            addSqcz.setHanleGenericPlan(sqczDO.getHanleGenericPlan());
            addSqcz.setHandleType(sqczDO.getHandleType());
            addSqcz.setStatus("0");
            addSqczList.add(addSqcz);
            //查询推送用户
            LambdaQueryWrapper<SqglSqdjtsdxDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(SqglSqdjtsdxDO::getSqczId, sqczDO.getId());
            List<SqglSqdjtsdxDO> sjkTsdxList = sqglSqdjtsdxService.list(lambdaQueryWrapper);
            for (SqglSqdjtsdxDO sqdjtsdxDO : sjkTsdxList) {
                SqglSqdjtsdxDO temp = new SqglSqdjtsdxDO();
                temp.setSqczId(addSqcz.getId());
                temp.setSqdjId(sqglSqdjDO.getId());
                temp.setPushUserSfzh(sqdjtsdxDO.getPushUserSfzh());
                temp.setPushUserName(sqdjtsdxDO.getPushUserName());
                temp.setPushPostCode(sqdjtsdxDO.getPushPostCode());
                temp.setPushPostName(sqdjtsdxDO.getPushPostName());
                temp.setPushTime(new Date());
                tsdxList.add(temp);
                List<SqglSqdjtsdxDO> tempList = new ArrayList<>();
                if (tsdxMap.containsKey(sqczDO.getHandlePostCode())) {
                    tempList = tsdxMap.get(sqczDO.getHandlePostCode());
                }
                tempList.add(temp);
                tsdxMap.put(sqczDO.getHandlePostCode(), tempList);
            }
        }

        sqglSqczDO.setStatus("3");
        sqglSqdjDO.setStatus("1");


        sqglSqczService.updateById(sqglSqczDO);
        updateById(sqglSqdjDO);

        sqglSqczService.saveBatch(addSqczList);
        sqglSqdjtsdxService.saveBatch(tsdxList);

        LambdaQueryWrapper<SqglBjldszDO> bjldszDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bjldszDOLambdaQueryWrapper.eq(SqglBjldszDO::getOrgCode, SessionUserUtil.getSessionUser().getOrgCode())
                .eq(SqglBjldszDO::getEventSrc, sqglSqdjDO.getEventSrc());

        SqglBjldszDO sqglBjldszDO = sqglBjldszService.getOne(bjldszDOLambdaQueryWrapper);

        //发送websocket消息到页面
        if (ObjectUtil.isNotEmpty(sqglBjldszDO.getOptionalLinkageSettings()) && sqglBjldszDO.getOptionalLinkageSettings().contains("sqtc")) {
            JSONObject msg = new JSONObject();
            msg.put("sqdjId", sqglSqdjDO.getId());
            msg.put("eventLevel", sqglSqdjDO.getEventLevel());
            msg.put("happenTime", sqglSqdjDO.getEventStartTime());
            msg.put("eventDetails", sqglSqdjDO.getEventDetails());
            send(msg, tsdxList);
        }

        //发送待办消息
        MsgAddVO msgAddVO = new MsgAddVO();
        msgAddVO.setBusinessId(sqglSqdjDO.getId());
        msgAddVO.setMsgType("108_001");
        Map<String, Object> contentData = new HashMap<>();
        AreaPrisonRoomDO areaPrisonRoomDO = areaPrisonRoomService.getAreaPrisonRoom(sqglSqdjDO.getAreaId());
        if (ObjectUtil.isNotEmpty(areaPrisonRoomDO)) {
            contentData.put("areaName", areaPrisonRoomDO.getAreaName());
        }
        contentData.put("roomName", sqglSqdjDO.getAreaName());
        contentData.put("eventType", sqglSqdjDO.getEventType());

        LambdaQueryWrapper<SqglSqdjGlryDO> glryDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        glryDOLambdaQueryWrapper.select(SqglSqdjGlryDO::getPersonnelName).eq(SqglSqdjGlryDO::getSqdjId, sqglSqdjDO.getId())
                .eq(SqglSqdjGlryDO::getPersonnelType, "1");
        List<SqglSqdjGlryDO> jgryList = sqglSqdjGlryService.list(glryDOLambdaQueryWrapper);

        StringBuilder bjgryXm = new StringBuilder();
        if (CollectionUtil.isNotEmpty(jgryList)) {
            for (int i = 0; i < jgryList.size(); i++) {
                bjgryXm.append(jgryList.get(i).getPersonnelName());
                if (i < jgryList.size() - 1) {
                    bjgryXm.append("、");
                }
            }
        }

        contentData.put("bjgryXm", bjgryXm.toString());
        msgAddVO.setContentData(contentData);
        msgAddVO.setModuleCode("ACP_JJKS_SQCZDB");
        msgAddVO.setUrl(String.format("/discipline/institutionSituation/inspectionOrCheck?id=%s&type=1", sqglSqdjDO.getId()));
        msgAddVO.setSpecify(true);

        for (Map.Entry<String, List<SqglSqdjtsdxDO>> map : tsdxMap.entrySet()) {
            msgAddVO.setPcid(sqglSqdjDO.getId() + ":" + map.getKey());
            List<String> idCardlist = map.getValue().stream().map(SqglSqdjtsdxDO::getPushUserSfzh).collect(Collectors.toList());
            List<OrgUserRespDTO> orgUserRespDTOList = bspApi.getUserByIdCards(String.join(",", idCardlist));
            List<ReceiveUser> receiveUserList = BeanUtils.toBean(orgUserRespDTOList, ReceiveUser.class);
            msgAddVO.setSpecifyReceiveUserList(receiveUserList);
            MsgUtil.sendMsg(msgAddVO);
        }

        return true;
    }

    @Override
    public SqglSqdjRespVO getSqglSqdjById(String id) {
        SqglSqdjDO sqglSqdjDO = getById(id);
        SqglSqdjRespVO res = BeanUtils.toBean(sqglSqdjDO, SqglSqdjRespVO.class);


        List<SqglSqdjGlrySaveReqVO> bjryList = new ArrayList<>();
        List<SqglSqdjGlrySaveReqVO> jgryList = new ArrayList<>();
        List<SqglSqdjGlrySaveReqVO> gzryList = new ArrayList<>();
        List<SqglSqdjGlrySaveReqVO> wlryList = new ArrayList<>();

        LambdaQueryWrapper<SqglSqdjGlryDO> glryDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        glryDOLambdaQueryWrapper.eq(SqglSqdjGlryDO::getSqdjId, id);
        List<SqglSqdjGlryDO> glryDOList = sqglSqdjGlryService.list(glryDOLambdaQueryWrapper);
        for (SqglSqdjGlryDO glryDO : glryDOList) {
            if ("1".equals(glryDO.getPersonnelType())) {
                jgryList.add(BeanUtils.toBean(glryDO, SqglSqdjGlrySaveReqVO.class));
            } else if ("2".equals(glryDO.getPersonnelType())) {
                gzryList.add(BeanUtils.toBean(glryDO, SqglSqdjGlrySaveReqVO.class));
            } else if ("3".equals(glryDO.getPersonnelType())) {
                wlryList.add(BeanUtils.toBean(glryDO, SqglSqdjGlrySaveReqVO.class));
            } else if ("4".equals(glryDO.getPersonnelType())) {
                bjryList.add(BeanUtils.toBean(glryDO, SqglSqdjGlrySaveReqVO.class));
            }
        }
        res.setBjryList(bjryList);
        res.setJgryList(jgryList);
        res.setGzryList(gzryList);
        res.setWlryList(wlryList);

        LambdaQueryWrapper<SqglSqczDO> sqglSqczDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sqglSqczDOLambdaQueryWrapper.eq(SqglSqczDO::getSqdjId, res.getId()).orderByAsc(SqglSqczDO::getAddTime);
        List<SqglSqczDO> sqczDOList = sqglSqczService.list(sqglSqczDOLambdaQueryWrapper);
        //创建节点进度情况
        res.setJdList(createNodeProgress(sqglSqdjDO, sqczDOList));
        //创建业务轨迹
        res.setTrajectoryList(createTrajectory(sqglSqdjDO, sqczDOList));

        return res;
    }

    /**
     * 创建节点进度情况
     *
     * @param sqglSqdjDO
     * @param sqczDOList
     * @return
     */
    private List<JSONObject> createNodeProgress(SqglSqdjDO sqglSqdjDO, List<SqglSqczDO> sqczDOList) {
        List<JSONObject> nodeList = new ArrayList<>();

        if ("0".equals(sqglSqdjDO.getStatus())) {
            //如果处于待核实状态，sqcz表只会有一条数据
            JSONObject node = new JSONObject();
            node.put("postName", sqczDOList.get(0).getHandlePostName());
            node.put("handleStatus", sqczDOList.get(0).getStatus());
            node.put("handleStatusName", "0".equals(sqczDOList.get(0).getStatus()) ? "未办结" : "已办结");
            nodeList.add(node);
            return nodeList;
        }

        //这里过滤最新的数据
        Map<String, SqglSqczDO> lastSqczMap = new HashMap<>();
        for (SqglSqczDO sqczDO : sqczDOList) {
            if (lastSqczMap.containsKey(sqczDO.getHandlePostCode() + ":" + sqczDO.getHandleType())) {
                //比较，取最新的那个
                SqglSqczDO temp = lastSqczMap.get(sqczDO.getHandlePostCode() + ":" + sqczDO.getHandleType());
                if (sqczDO.getAddTime().after(temp.getAddTime())) {
                    lastSqczMap.put(sqczDO.getHandlePostCode() + ":" + sqczDO.getHandleType(), sqczDO);
                }
            } else {
                lastSqczMap.put(sqczDO.getHandlePostCode() + ":" + sqczDO.getHandleType(), sqczDO);
            }
        }

        //根据状态再次组装
        SqglSqczDO fistNode = new SqglSqczDO();
        List<SqglSqczDO> middleNodeList = new ArrayList<>();
        SqglSqczDO leaderNode = new SqglSqczDO();

        for (Map.Entry<String, SqglSqczDO> map : lastSqczMap.entrySet()) {
            if (map.getKey().contains(":1")) {
                fistNode = map.getValue();
            } else if (map.getKey().contains(":2")) {
                middleNodeList.add(map.getValue());
            } else if (map.getKey().contains(":3")) {
                leaderNode = map.getValue();
            }
        }

        JSONObject fistJsonNode = new JSONObject();
        fistJsonNode.put("postName", fistNode.getHandlePostName());
        fistJsonNode.put("handleStatus", fistNode.getStatus());
        fistJsonNode.put("handleStatusName", "已办结");
        nodeList.add(fistJsonNode);

        for (SqglSqczDO sqczDO : middleNodeList) {
            JSONObject middleJsonNode = new JSONObject();
            middleJsonNode.put("postName", sqczDO.getHandlePostName());
            middleJsonNode.put("handleStatus", sqczDO.getStatus());
            middleJsonNode.put("handleStatusName", "0".equals(sqczDO.getStatus()) ? "未办结" : "已办结");
            nodeList.add(middleJsonNode);
        }

        JSONObject leaderJsonNode = new JSONObject();
        if ("1".equals(sqglSqdjDO.getStatus())) {
            leaderJsonNode.put("postName", "所领导");
            leaderJsonNode.put("handleStatus", "0");
            leaderJsonNode.put("handleStatusName", "未办结");
        } else {
            leaderJsonNode.put("postName", "所领导");
            leaderJsonNode.put("handleStatus", "0".equals(leaderNode.getStatus()) ? "0" : "1");
            leaderJsonNode.put("handleStatusName", "0".equals(leaderNode.getStatus()) ? "未办结" : "已办结");
        }
        nodeList.add(leaderJsonNode);
        return nodeList;
    }

    /**
     * 创建业务轨迹
     *
     * @param sqglSqdjDO
     * @param sqczDOList
     * @return
     */
    private List<SqglSqczRespVO> createTrajectory(SqglSqdjDO sqglSqdjDO, List<SqglSqczDO> sqczDOList) {
        List<SqglSqczRespVO> trajectoryNodeList = new ArrayList<>();
        Map<String, String> postNameMap = new HashMap<>();
        sqczDOList.forEach(x -> {
            postNameMap.put(x.getId(), x.getHandlePostName());
        });

        for (SqglSqczDO sqczDO : sqczDOList) {
            if ("0".equals(sqczDO.getStatus())) {
                continue;
            }
            SqglSqczRespVO sqglSqczRespVO = BeanUtils.toBean(sqczDO, SqglSqczRespVO.class);
            if (ObjectUtil.isNotEmpty(sqglSqczRespVO.getAttUrl())) {
                sqglSqczRespVO.setAttUrl(wbCommonService.getFile(sqczDO.getAttUrl()));
            }
            //管控岗位，需要返回关联业务
            if ("2".equals(sqczDO.getHandleType()) && "03".equals(sqczDO.getHandlePostCode())) {
                LambdaQueryWrapper<SqglSqczGywlDO> gywlDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                gywlDOLambdaQueryWrapper.eq(SqglSqczGywlDO::getSqczId, sqczDO.getId());
                List<SqglSqczGywlDO> gywlDOList = sqglSqczGywlService.list(gywlDOLambdaQueryWrapper);
                if (CollectionUtil.isNotEmpty(gywlDOList)) {
                    sqglSqczRespVO.setGlywList(BeanUtils.toBean(gywlDOList, SqglSqczGywlRespVO.class));
                }
            }
            if ("3".equals(sqczDO.getHandleType())) {
                List<SqglSqczApproveChildRespVO> approveInfoList = new ArrayList<>();
                List<JSONObject> approveJsonList = JSONArray.parseArray(sqczDO.getApproveInfo(), JSONObject.class);
                for (JSONObject temp : approveJsonList) {
                    SqglSqczApproveChildRespVO respVO = BeanUtils.toBean(temp, SqglSqczApproveChildRespVO.class);
                    respVO.setPostName(postNameMap.get(temp.getString("sqczId")));
                    approveInfoList.add(respVO);
                }
                sqglSqczRespVO.setApproveInfoList(approveInfoList);
            }
            trajectoryNodeList.add(sqglSqczRespVO);
        }
        return trajectoryNodeList;
    }

    @Override
    public SqglSqczRespVO getCurrent(String id) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();

        SqglSqdjDO sqglSqdjDO = getById(id);
        LambdaQueryWrapper<SqglSqdjtsdxDO> sqdjtsdxDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sqdjtsdxDOLambdaQueryWrapper.eq(SqglSqdjtsdxDO::getSqdjId, id)
                .eq(SqglSqdjtsdxDO::getPushUserSfzh, sessionUser.getIdCard());
        List<SqglSqdjtsdxDO> sqglSqdjtsdxDOList = sqglSqdjtsdxService.list(sqdjtsdxDOLambdaQueryWrapper);
        if (CollectionUtil.isEmpty(sqglSqdjtsdxDOList)) {
            return null;
        }
        //正常是一个岗位一个用户进行处理，但不排除一个用户多个岗位，然后这个用户又都被选择了
        String handleType = "";
        if ("0".equals(sqglSqdjDO.getStatus())) {
            handleType = "1";
        } else if ("1".equals(sqglSqdjDO.getStatus())) {
            handleType = "2";
        } else if ("2".equals(sqglSqdjDO.getStatus())) {
            handleType = "3";
        }

        List<String> czIdList = sqglSqdjtsdxDOList.stream().map(SqglSqdjtsdxDO::getSqczId).collect(Collectors.toList());
        LambdaQueryWrapper<SqglSqczDO> sqczDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sqczDOLambdaQueryWrapper.in(SqglSqczDO::getId, czIdList).eq(SqglSqczDO::getStatus, "0")
                .eq(SqglSqczDO::getHandleType, handleType);
        List<SqglSqczDO> sqczDOList = sqglSqczService.list(sqczDOLambdaQueryWrapper);
        if (CollectionUtil.isEmpty(sqczDOList)) {
            return null;
        }

        //随缘取第一个
        SqglSqczRespVO res = BeanUtils.toBean(sqczDOList.get(0), SqglSqczRespVO.class);
        if ("2".equals(res.getHandleType()) && "03".equals(res.getHandlePostCode())) {
            //查询当前岗位可办的关联业务
            List<JSONObject> allowList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(sqglSqdjDO.getEventTemplateId())) {
                SqglMbpzDO sqglMbpzDO = sqglMbpzService.getById(sqglSqdjDO.getEventTemplateId());
                List<SqglMbpzTsdxRespVO> pushObjectSettingList = JSONArray.parseArray(sqglMbpzDO.getPushObjectSettings(), SqglMbpzTsdxRespVO.class);
                for (SqglMbpzTsdxRespVO sqglMbpzTsdxRespVO : pushObjectSettingList) {
                    if ("03".equals(sqglMbpzTsdxRespVO.getDisposePost())) {
                        String disposeBusiness = sqglMbpzTsdxRespVO.getDisposeBusiness();
                        if (ObjectUtil.isNotEmpty(disposeBusiness)) {
                            List<String> disposeBusinessList = Arrays.asList(disposeBusiness.split(","));
                            for (String ywStr : disposeBusinessList) {
                                JSONObject allowJson = new JSONObject();
                                allowJson.put("businessType", ywStr);
                                allowJson.put("businessTypeName", DicUtils.translate("ZD_JJKS_GLYW", ywStr));
                                allowList.add(allowJson);
                            }
                        }
                    }
                }
            }
            if (CollectionUtil.isEmpty(allowList)) {
                //如果为空，则默认全部业务都可以办理
                Map<Object, Object> glywMap = bspSdk.getDic(systemMark, "ZD_JJKS_GLYW");
                for (Map.Entry<Object, Object> glyw : glywMap.entrySet()) {
                    JSONObject allowJson = new JSONObject();
                    allowJson.put("businessType", String.valueOf(glyw.getKey()));
                    allowJson.put("businessTypeName", String.valueOf(glyw.getValue()));
                    allowList.add(allowJson);
                }
            }
            res.setAllowList(allowList);
        }
        if ("3".equals(res.getHandleType())) {
            List<SqglSqczDO> lastMiddleSqczList = getLastMiddleSqczList(sqglSqdjDO.getId());
            List<SqglSqczApproveChildRespVO> approveInfoList = new ArrayList<>();
            for (SqglSqczDO sqczDO : lastMiddleSqczList) {
                SqglSqczApproveChildRespVO childRespVO = new SqglSqczApproveChildRespVO();
                childRespVO.setSqczId(sqczDO.getId());
                childRespVO.setPostName(sqczDO.getHandlePostName());
                approveInfoList.add(childRespVO);
            }
            res.setApproveInfoList(approveInfoList);
        }
        return res;
    }

    private List<SqglSqczDO> getLastMiddleSqczList(String sqdjId) {
        LambdaQueryWrapper<SqglSqczDO> sqczDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sqczDOLambdaQueryWrapper.eq(SqglSqczDO::getSqdjId, sqdjId).eq(SqglSqczDO::getHandleType, "2");
        List<SqglSqczDO> sqczDOList = sqglSqczService.list(sqczDOLambdaQueryWrapper);
        Map<String, SqglSqczDO> lastMap = new HashMap<>();
        for (SqglSqczDO sqczDO : sqczDOList) {
            if (lastMap.containsKey(sqczDO.getHandlePostName())) {
                SqglSqczDO temp = lastMap.get(sqczDO.getHandlePostCode());
                if (sqczDO.getAddTime().after(temp.getAddTime())) {
                    lastMap.put(sqczDO.getHandlePostCode(), sqczDO);
                }
            } else {
                lastMap.put(sqczDO.getHandlePostCode(), sqczDO);
            }
        }
        List<SqglSqczDO> resList = new ArrayList<>();
        for (Map.Entry<String, SqglSqczDO> map : lastMap.entrySet()) {
            resList.add(map.getValue());
        }
        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWaitVerify(String orgCode, String orgName, SqglSqdjSaveReqVO createReqVO) {
        log.info("开始创建待核实所情登记，机构代码：{}，机构名称：{}，事件源：{}，告警类型：{}",
                orgCode, orgName, createReqVO.getEventSrc(), createReqVO.getAlarmType());

        SqglSqdjDO sqglSqdj = BeanUtils.toBean(createReqVO, SqglSqdjDO.class);

        sqglSqdj.setId(StringUtil.getGuid32());
        sqglSqdj.setOrgCode(orgCode);
        sqglSqdj.setOrgName(orgName);

        log.debug("生成所情登记ID：{}", sqglSqdj.getId());

        //所情编码
        String eventCode = null;
        try {
            eventCode = bspApi.executeByRuleCode("acp_prison_event_no", null);
        } catch (Exception e) {
            log.error("生成所情编码失败", e);
        }
        if (ObjectUtil.isEmpty(eventCode)) {
            //降级处理
            eventCode = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        }
        sqglSqdj.setEventCode(eventCode);
        log.info("生成所情编码：{}", eventCode);

        if (ObjectUtil.isNotEmpty(createReqVO.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getAlarmType())) {
            log.debug("开始查询所情模板配置，事件源：{}，告警类型：{}", createReqVO.getEventSrc(), createReqVO.getAlarmType());

            LambdaQueryWrapper<SqglMbpzDO> mbpzDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            mbpzDOLambdaQueryWrapper.eq(SqglMbpzDO::getAlarmType, createReqVO.getAlarmType())
                    .eq(SqglMbpzDO::getEventSrc, createReqVO.getEventSrc());
            List<SqglMbpzDO> sqglMbpzDOList = sqglMbpzService.list(mbpzDOLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(sqglMbpzDOList)) {
                sqglSqdj.setEventTemplateId(sqglMbpzDOList.get(0).getId());
                sqglSqdj.setEventName(sqglMbpzDOList.get(0).getTemplateName());
                log.info("找到所情模板配置，模板ID：{}，模板名称：{}", sqglMbpzDOList.get(0).getId(), sqglMbpzDOList.get(0).getTemplateName());
            } else {
                log.warn("未找到对应的所情模板配置，事件源：{}，告警类型：{}", createReqVO.getEventSrc(), createReqVO.getAlarmType());
            }

            log.debug("开始查询告警类型配置，机构代码：{}", orgCode);
            LambdaQueryWrapper<SqglGjlxpzDO> sqglGjlxpzDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sqglGjlxpzDOLambdaQueryWrapper.eq(SqglGjlxpzDO::getEventSrc, createReqVO.getEventSrc())
                    .eq(SqglGjlxpzDO::getAlarmType, createReqVO.getAlarmType()).eq(SqglGjlxpzDO::getOrgCode, orgCode);
            SqglGjlxpzDO sqglGjlxpzDO = sqglGjlxpzService.getOne(sqglGjlxpzDOLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(sqglGjlxpzDO)) {
                sqglSqdj.setEventLevel(sqglGjlxpzDO.getEventLevel());
                if (ObjectUtil.isEmpty(sqglSqdj.getEventName())) {
                    sqglSqdj.setEventName(DicUtils.translate("ZD_JJKS_GJLX", sqglGjlxpzDO.getAlarmType()));
                }
                log.info("找到告警类型配置，事件级别：{}，事件名称：{}", sqglGjlxpzDO.getEventLevel(), sqglSqdj.getEventName());
            } else {
                log.warn("未找到告警类型配置，机构代码：{}，事件源：{}，告警类型：{}", orgCode, createReqVO.getEventSrc(), createReqVO.getAlarmType());
            }

            //为空默认4级
            if (ObjectUtil.isEmpty(sqglSqdj.getEventLevel())) {
                sqglSqdj.setEventLevel("4");
                log.info("事件级别为空，设置为默认4级");
            }

        }

        //判断是否是手环告警，如果是，则关联查询下被监管人员
        if ("003".equals(sqglSqdj.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getBjgrybm())) {
            log.info("处理手环告警，被监管人员编码：{}", createReqVO.getBjgrybm());
            PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(createReqVO.getBjgrybm());
            if (ObjectUtil.isNotEmpty(prisonerVwRespVO)) {
                SqglSqdjGlryDO glryDO = new SqglSqdjGlryDO();
                glryDO.setSqdjId(sqglSqdj.getId());
                glryDO.setOrgCode(orgCode);
                glryDO.setOrgName(orgName);
                glryDO.setPersonnelType("1");
                glryDO.setPersonnelId(createReqVO.getBjgrybm());
                glryDO.setPersonnelName(prisonerVwRespVO.getXm());
                glryDO.setPhotoUrl(prisonerVwRespVO.getFrontPhoto());
                glryDO.setRoomId(prisonerVwRespVO.getJsh());
                glryDO.setRoomName(prisonerVwRespVO.getRoomName());
                sqglSqdjGlryService.save(glryDO);
                log.info("成功关联被监管人员信息，姓名：{}，监室：{}", prisonerVwRespVO.getXm(), prisonerVwRespVO.getRoomName());
            } else {
                log.warn("未找到被监管人员信息，编码：{}", createReqVO.getBjgrybm());
            }
        }


        //视频分析高级
        if ("002".equals(sqglSqdj.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getThirdDeviceId())) {
            log.info("处理视频分析告警，第三方设备ID：{}", createReqVO.getThirdDeviceId());
            List<JSONObject> areaList = sqglSqdjDao.getAreaByGbcode(createReqVO.getThirdDeviceId());
            if (CollectionUtil.isNotEmpty(areaList)) {
                sqglSqdj.setAreaId(areaList.get(0).getString("area_id"));
                sqglSqdj.setAreaName(areaList.get(0).getString("area_name"));
                log.info("成功关联视频设备区域，区域ID：{}，区域名称：{}", sqglSqdj.getAreaId(), sqglSqdj.getAreaName());
            } else {
                log.warn("未找到视频设备对应的区域信息，设备ID：{}", createReqVO.getThirdDeviceId());
            }
        }

        //周界
        if ("006".equals(sqglSqdj.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getThirdDeviceId())) {
            log.info("处理周界告警，第三方设备ID：{}", createReqVO.getThirdDeviceId());
            //查询设备
            LambdaQueryWrapper<BaseDeviceDO> deviceDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            deviceDOLambdaQueryWrapper.select(BaseDeviceDO::getId, BaseDeviceDO::getDeviceName, BaseDeviceDO::getDeviceCode,
                    BaseDeviceDO::getRefDeviceId, BaseDeviceDO::getAreaId);
            deviceDOLambdaQueryWrapper.eq(BaseDeviceDO::getDeviceCode, createReqVO.getThirdDeviceId())
                    .eq(BaseDeviceDO::getDeviceTypeId, "0035");
            List<BaseDeviceDO> deviceDOList = baseDeviceService.list(deviceDOLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(deviceDOList)) {
                //取第一个
                BaseDeviceDO device = deviceDOList.get(0);
                log.debug("找到周界设备，设备名称：{}，区域ID：{}", device.getDeviceName(), device.getAreaId());
                AreaDO areaDO = areaService.getArea(device.getAreaId());
                if (ObjectUtil.isNotEmpty(areaDO)) {
                    sqglSqdj.setAreaId(areaDO.getId());
                    sqglSqdj.setAreaName(areaDO.getAreaName());
                    log.info("成功关联周界设备区域，区域ID：{}，区域名称：{}", areaDO.getId(), areaDO.getAreaName());
                } else {
                    log.warn("未找到周界设备对应的区域信息，区域ID：{}", device.getAreaId());
                }
            } else {
                log.warn("未找到周界设备信息，设备编码：{}", createReqVO.getThirdDeviceId());
            }
        }

        //快鱼音频
        if ("010".equals(sqglSqdj.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getThirdDeviceId())) {
            log.info("处理快鱼音频告警，第三方设备ID：{}", createReqVO.getThirdDeviceId());
            //查询设备
            LambdaQueryWrapper<BaseDeviceDO> deviceDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            deviceDOLambdaQueryWrapper.select(BaseDeviceDO::getId, BaseDeviceDO::getDeviceName, BaseDeviceDO::getDeviceCode,
                    BaseDeviceDO::getRefDeviceId, BaseDeviceDO::getRoomId);
            deviceDOLambdaQueryWrapper.eq(BaseDeviceDO::getDeviceCode, createReqVO.getThirdDeviceId())
                    .eq(BaseDeviceDO::getDeviceTypeId, "0036");
            List<BaseDeviceDO> deviceDOList = baseDeviceService.list(deviceDOLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(deviceDOList)) {
                //取第一个
                BaseDeviceDO device = deviceDOList.get(0);
                log.debug("找到快鱼音频设备，设备名称：{}，房间ID：{}", device.getDeviceName(), device.getRoomId());
                AreaDO areaDO = areaService.getArea(device.getRoomId());
                if (ObjectUtil.isNotEmpty(areaDO)) {
                    sqglSqdj.setAreaId(areaDO.getId());
                    sqglSqdj.setAreaName(areaDO.getAreaName());
                    log.info("成功关联快鱼音频设备区域，区域ID：{}，区域名称：{}", areaDO.getId(), areaDO.getAreaName());
                } else {
                    log.warn("未找到快鱼音频设备对应的区域信息，房间ID：{}", device.getRoomId());
                }
            } else {
                log.warn("未找到快鱼音频设备信息，设备编码：{}", createReqVO.getThirdDeviceId());
            }
        }

        //更改状态--待处置
        sqglSqdj.setStatus(SuoQingHeShiStatus.PENDING.getCode());
        log.info("设置所情登记状态为待核实(0)");

        //创建巡控岗的处置
        SqglSqczDO xkSqcz = new SqglSqczDO();
        String sqczId = StringUtil.getGuid32();
        xkSqcz.setId(sqczId);
        xkSqcz.setOrgCode(orgCode);
        xkSqcz.setOrgName(orgName);
        xkSqcz.setSqdjId(sqglSqdj.getId());
        xkSqcz.setHandleTime(new Date());
        xkSqcz.setHandleType("1");
        xkSqcz.setHandlePostCode("02");
        xkSqcz.setHandlePostName("巡控岗");
        xkSqcz.setStatus("0");
        log.info("创建巡控岗处置记录，处置ID：{}", sqczId);

        //查询该岗位下的全部用户
        log.debug("开始查询巡控岗用户，机构代码：{}", orgCode);
        List<UserRespDTO> userRespDTOList = userApi.getUserByOrgAndPost(orgCode, "02");
        if (CollectionUtil.isEmpty(userRespDTOList)) {
            log.warn("巡控岗下未找到任何用户，机构代码：{}", orgCode);
        } else {
            log.info("找到巡控岗用户{}个", userRespDTOList.size());
        }

        List<SqglSqdjtsdxDO> sqglSqdjtsdxDOList = new ArrayList<>();
        //推送至电子地图
        SqglSqdjtsdxDO sqdjtsdxDzdtDO = new SqglSqdjtsdxDO();
        sqdjtsdxDzdtDO.setSqczId(xkSqcz.getId());
        sqdjtsdxDzdtDO.setSqdjId(sqglSqdj.getId());
        sqdjtsdxDzdtDO.setOrgCode(orgCode);
        sqdjtsdxDzdtDO.setOrgName(orgName);
        sqdjtsdxDzdtDO.setPushUserSfzh(orgCode);
        sqdjtsdxDzdtDO.setPushUserName(orgCode);
        sqdjtsdxDzdtDO.setPushPostCode("02");
        sqdjtsdxDzdtDO.setPushPostName("巡控岗");
        sqdjtsdxDzdtDO.setPushTime(new Date());
        sqglSqdjtsdxDOList.add(sqdjtsdxDzdtDO);

        for (UserRespDTO userRespDTO : userRespDTOList) {
            SqglSqdjtsdxDO sqdjtsdxDO = new SqglSqdjtsdxDO();
            sqdjtsdxDO.setSqczId(xkSqcz.getId());
            sqdjtsdxDO.setSqdjId(sqglSqdj.getId());
            sqdjtsdxDO.setOrgCode(orgCode);
            sqdjtsdxDO.setOrgName(orgName);
            sqdjtsdxDO.setPushUserSfzh(userRespDTO.getIdCard());
            sqdjtsdxDO.setPushUserName(userRespDTO.getName());
            sqdjtsdxDO.setPushPostCode("02");
            sqdjtsdxDO.setPushPostName("巡控岗");
            sqdjtsdxDO.setPushTime(new Date());
            sqglSqdjtsdxDOList.add(sqdjtsdxDO);
            log.debug("创建推送对象，用户：{}，身份证：{}", userRespDTO.getName(), userRespDTO.getIdCard());
        }


        log.info("开始保存数据到数据库");
        sqglSqczService.save(xkSqcz);
        log.debug("保存巡控岗处置记录成功");

        sqglSqdjtsdxService.saveBatch(sqglSqdjtsdxDOList);
        log.debug("批量保存推送对象成功，数量：{}", sqglSqdjtsdxDOList.size());

        save(sqglSqdj);
        log.info("保存所情登记记录成功，所情ID：{}", sqglSqdj.getId());

        log.debug("查询报警联动设置");
        LambdaQueryWrapper<SqglBjldszDO> bjldszDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bjldszDOLambdaQueryWrapper.eq(SqglBjldszDO::getOrgCode, orgCode)
                .eq(SqglBjldszDO::getEventSrc, sqglSqdj.getEventSrc());

        SqglBjldszDO sqglBjldszDO = sqglBjldszService.getOne(bjldszDOLambdaQueryWrapper);

        //发送websocket消息到页面
        //报警联动设置-所情弹窗
        if (ObjectUtil.isNotEmpty(sqglBjldszDO) && ObjectUtil.isNotEmpty(sqglBjldszDO.getOptionalLinkageSettings())
                && sqglBjldszDO.getOptionalLinkageSettings().contains("sqtc")) {
            log.info("满足WebSocket推送条件，开始发送实时消息");
            SqglSqdjRespVO sqglSqdjRespVO = BeanUtil.toBean(sqglSqdj, SqglSqdjRespVO.class);
            transService.transOne(sqglSqdjRespVO);
            Map<String, Object> msg = BeanUtil.beanToMap(sqglSqdjRespVO);
            msg.put("sqdjId", sqglSqdj.getId());
            msg.put("eventLevel", createReqVO.getEventLevel());
            msg.put("happenTime", DateUtil.format(createReqVO.getEventStartTime(), "yyyy-MM-dd HH:mm:ss"));
            msg.put("eventDetails", createReqVO.getEventDetails());
            msg.put("url", String.format("/discipline/institutionSituation/settle?id=%s", sqglSqdj.getId()));
            msg.put("status", sqglSqdj.getStatus());
            //关联实时视频
            if (sqglBjldszDO.getOptionalLinkageSettings().contains("sssp")) {
                List<RoomVideoVO> roomVideoVOS = baseDeviceCameraService.roomVideo(sqglSqdj.getAreaId());
                msg.put("roomVideo", roomVideoVOS);
            }

            try {
                // 直接从对象转为 JSONObject，保留 null 字段
                String jsonStr = JSONObject.toJSONString(msg, SerializerFeature.WriteMapNullValue);
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                send(jsonObject, sqglSqdjtsdxDOList);
                log.info("WebSocket消息发送成功，推送用户数量：{}", sqglSqdjtsdxDOList.size());
            } catch (Exception e) {
                log.error("WebSocket消息发送失败", e);
            }
        } else {
            log.debug("不满足WebSocket推送条件，跳过实时消息发送");
        }

        log.info("createWaitVerify方法执行完成，所情ID：{}，所情编码：{}", sqglSqdj.getId(), sqglSqdj.getEventCode());
    }

    /**
     * 无需处置
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean noActionRequired(String id) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SqglSqdjDO sqglSqdjDO = getById(id);
        sqglSqdjDO.setStatus("3");
        //巡控环节未办结的改为办结
        LambdaUpdateWrapper<SqglSqczDO> sqglSqczDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sqglSqczDOLambdaUpdateWrapper.eq(SqglSqczDO::getSqdjId, id)
                .eq(SqglSqczDO::getHandleType, "1")
                .eq(SqglSqczDO::getStatus, "0");

        sqglSqczDOLambdaUpdateWrapper.set(SqglSqczDO::getStatus, "1")
                .set(SqglSqczDO::getHandleUserSfzh, sessionUser.getIdCard())
                .set(SqglSqczDO::getHandleUserName, sessionUser.getName())
                .set(SqglSqczDO::getHandleTime, new Date());
        sqglSqczService.update(sqglSqczDOLambdaUpdateWrapper);
        return updateById(sqglSqdjDO);
    }

    @Override
    public boolean ycz(String id) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SqglSqdjDO sqglSqdjDO = getById(id);
        sqglSqdjDO.setStatus(SuoQingHeShiStatus.NOT_REQUIRED.getCode());

        //巡控环节未办结的改为办结
        LambdaUpdateWrapper<SqglSqczDO> sqglSqczDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sqglSqczDOLambdaUpdateWrapper.eq(SqglSqczDO::getSqdjId, id)
                .eq(SqglSqczDO::getHandleType, "1")
                .eq(SqglSqczDO::getStatus, "0");

        sqglSqczDOLambdaUpdateWrapper.set(SqglSqczDO::getStatus, "1")
                .set(SqglSqczDO::getHandleUserSfzh, sessionUser.getIdCard())
                .set(SqglSqczDO::getHandleUserName, sessionUser.getName())
                .set(SqglSqczDO::getHandleTime, new Date());
        sqglSqczService.update(sqglSqczDOLambdaUpdateWrapper);
        return updateById(sqglSqdjDO);
    }


    /**
     * 误报
     *
     * @param id
     * @return
     */

    @Override
    public boolean wb(String id) {
        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SqglSqdjDO sqglSqdjDO = getById(id);
        sqglSqdjDO.setStatus(SuoQingHeShiStatus.WB.getCode());

        //巡控环节未办结的改为办结
        LambdaUpdateWrapper<SqglSqczDO> sqglSqczDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sqglSqczDOLambdaUpdateWrapper.eq(SqglSqczDO::getSqdjId, id)
                .eq(SqglSqczDO::getHandleType, "1")
                .eq(SqglSqczDO::getStatus, "0");

        sqglSqczDOLambdaUpdateWrapper.set(SqglSqczDO::getStatus, "1")
                .set(SqglSqczDO::getHandleUserSfzh, sessionUser.getIdCard())
                .set(SqglSqczDO::getHandleUserName, sessionUser.getName())
                .set(SqglSqczDO::getHandleTime, new Date());
        sqglSqczService.update(sqglSqczDOLambdaUpdateWrapper);
        return updateById(sqglSqdjDO);
    }

    @Override
    public boolean ts(SqglSqdjTsSaveReqVO sqglSqdjSaveReqVO) {

        SessionUser sessionUser = SessionUserUtil.getSessionUser();
        SqglSqdjDO sqglSqdj = BeanUtils.toBean(sqglSqdjSaveReqVO, SqglSqdjDO.class);
        LambdaUpdateWrapper<SqglSqczDO> sqglSqczDOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        //1中间环节的处置、巡控岗未办结，更新处理人
        sqglSqczDOLambdaUpdateWrapper.set(SqglSqczDO::getStatus, SuoQingHeShiStatus.IN_PROGRESS.getCode())
                .set(SqglSqczDO::getHandleUserSfzh, sessionUser.getIdCard())
                .set(SqglSqczDO::getHandleUserName, sessionUser.getName())
                .set(SqglSqczDO::getHandleTime, new Date());

        sqglSqczDOLambdaUpdateWrapper.eq(SqglSqczDO::getSqdjId, sqglSqdjSaveReqVO.getId())
                .eq(SqglSqczDO::getOrgCode, sessionUser.getOrgCode())
                .eq(SqglSqczDO::getHandleType, "1")
                .eq(SqglSqczDO::getStatus, "0");
        sqglSqczService.update(sqglSqczDOLambdaUpdateWrapper);

        sqglSqdj.setHandleUserName(sessionUser.getName());

        SqglSqdjSaveReqVO sqdjSaveReqVO = BeanUtil.toBean(sqglSqdj, SqglSqdjSaveReqVO.class);
        transService.transOne(sqdjSaveReqVO);
        sqdjSaveReqVO.setTsdxList(sqglSqdjSaveReqVO.getTsdxList());
        //创建中间环节的处置
        createMiddleNode(sqdjSaveReqVO, sqglSqdj.getId());
        //中间环节的处置：正在中间环节中被处理
        sqglSqdj.setStatus(SuoQingHeShiStatus.IN_PROGRESS.getCode());
        sqglSqdjDao.updateById(sqglSqdj);
        return updateById(sqglSqdj);
    }
}
