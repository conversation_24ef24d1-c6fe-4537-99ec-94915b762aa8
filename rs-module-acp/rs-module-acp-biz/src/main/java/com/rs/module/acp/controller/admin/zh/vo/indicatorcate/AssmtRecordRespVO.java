package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmtRecordRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("被考核人身份证号")
    private String assessedSfzh;
    @ApiModelProperty("被考核人姓名")
    private String assessedName;
    @ApiModelProperty("所属岗位")
    private String postName;
    @ApiModelProperty("考核月份")
    private String assmtMonth;
    @ApiModelProperty("指标类型ID")
    private String indicatorCateId;
    @ApiModelProperty("指标类型名称")
    private String indicatorCateName;
    @ApiModelProperty("截止日期")
    private Date expiryDate;
    @ApiModelProperty("加减分得分")
    private BigDecimal addsubtractScore;
    @ApiModelProperty("主观得分")
    private BigDecimal subjectiveScore;
    @ApiModelProperty("总得分")
    private BigDecimal totalScore;
    @ApiModelProperty("状态")
    @Trans(type = TransType.DICTIONARY, key = "ZD_JXKH_KHZT")
    private String status;

    @ApiModelProperty("主观详情")
    private AssmttApprovalRespVO subjectiveDetail;


    @ApiModelProperty("加减分详情")
    private List<AssmttApprovalRespVO> addsubtractList;
}
