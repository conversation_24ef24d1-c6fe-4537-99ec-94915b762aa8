package com.rs.module.acp.controller.admin.wb;

import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.wb.vo.GoodsDeliveryRespVO;
import com.rs.module.acp.controller.admin.wb.vo.GoodsDeliverySaveReqVO;
import com.rs.module.acp.enums.wb.WbConstants;
import com.rs.module.acp.service.wb.GoodsDeliveryService;
import com.rs.module.acp.service.wb.WbCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-物品顾送登记")
@RestController
@RequestMapping("/acp/wb/goodsDelivery")
@Validated
public class GoodsDeliveryController {

    @Resource
    private GoodsDeliveryService goodsDeliveryService;

    @Autowired
    private WbCommonService wbCommonService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建物品顾送登记")
    @LogRecordAnnotation(bizModule = "acp:goodsDelivery:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建物品顾送登记",
            success = "实战平台-窗口业务-创建物品顾送登记成功", fail = "实战平台-窗口业务-创建物品顾送登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createGoodsDelivery(@Valid @RequestBody GoodsDeliverySaveReqVO createReqVO) {
        return success(goodsDeliveryService.createGoodsDelivery(createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得物品顾送登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:goodsDelivery:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得物品顾送登记",
            success = "实战平台-窗口业务-获得物品顾送登记成功", fail = "实战平台-窗口业务-获得物品顾送登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<GoodsDeliveryRespVO> getGoodsDelivery(@RequestParam("id") String id) {
        return success(goodsDeliveryService.getGoodsDeliveryById(id));
    }

    @GetMapping("/getNewHistoryMeetingByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据监管人员编码获取监管人员历史物品顾送记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:goodsDelivery:getNewHistoryGoodsDeliveryByJgrybm", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-根据监管人员编码获取监管人员历史物品顾送记录",
            success = "实战平台-窗口业务-根据监管人员编码获取监管人员历史物品顾送记录成功",
            fail = "实战平台-窗口业务-根据监管人员编码获取监管人员历史物品顾送记录失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#params}}")
    public CommonResult<PageResult<JSONObject>> getNewHistoryMeetingByJgrybm(@RequestParam("jgrybm") String jgrybm,
                                                                             @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                             @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(wbCommonService.getHistoryMeetingByJgrybm(jgrybm,pageNo,pageSize, WbConstants.BUSINESS_TYPE_GOODS_DELIVERY));
    }
}
