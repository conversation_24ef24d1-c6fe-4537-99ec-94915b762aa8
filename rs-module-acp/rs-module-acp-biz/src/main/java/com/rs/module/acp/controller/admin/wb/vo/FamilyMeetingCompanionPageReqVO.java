package com.rs.module.acp.controller.admin.wb.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-家属会见同行登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FamilyMeetingCompanionPageReqVO extends PageParam {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("家属会见ID")
    private String familyMeetingId;

    @ApiModelProperty("律师姓名")
    private String name;

    @ApiModelProperty("律师性别")
    private String gender;

    @ApiModelProperty("律师证件号码")
    private String idCard;

    @ApiModelProperty("同行人类别")
    private String companionType;

    @ApiModelProperty("附件URL")
    private String attachmentUrl;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
