package com.rs.module.acp.controller.admin.sys;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigGlobalListReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigGlobalPageReqVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigGlobalRespVO;
import com.rs.module.acp.controller.admin.sys.vo.VbConfigGlobalSaveReqVO;
import com.rs.module.acp.entity.sys.VbConfigGlobalDO;
import com.rs.module.acp.service.sys.VbConfigGlobalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-语音播报-全局配置")
@RestController
@RequestMapping("/acp/sys/vbConfigGlobal")
@Validated
public class VbConfigGlobalController {

    @Resource
    private VbConfigGlobalService vbConfigGlobalService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-语音播报-全局配置")
    public CommonResult<String> createVbConfigGlobal(@Valid @RequestBody VbConfigGlobalSaveReqVO createReqVO) {
        return success(vbConfigGlobalService.createVbConfigGlobal(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-语音播报-全局配置")
    public CommonResult<Boolean> updateVbConfigGlobal(@Valid @RequestBody VbConfigGlobalSaveReqVO updateReqVO) {
        vbConfigGlobalService.updateVbConfigGlobal(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-语音播报-全局配置")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteVbConfigGlobal(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           vbConfigGlobalService.deleteVbConfigGlobal(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-语音播报-全局配置")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<VbConfigGlobalRespVO> getConfig(@RequestParam("orgCode") String orgCode) {
    	VbConfigGlobalDO config = vbConfigGlobalService.getConfig(orgCode);
        return success(BeanUtils.toBean(config, VbConfigGlobalRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-语音播报-全局配置分页")
    public CommonResult<PageResult<VbConfigGlobalRespVO>> getVbConfigGlobalPage(@Valid @RequestBody VbConfigGlobalPageReqVO pageReqVO) {
        PageResult<VbConfigGlobalDO> pageResult = vbConfigGlobalService.getVbConfigGlobalPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VbConfigGlobalRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-语音播报-全局配置列表")
    public CommonResult<List<VbConfigGlobalRespVO>> getVbConfigGlobalList(@Valid @RequestBody VbConfigGlobalListReqVO listReqVO) {
        List<VbConfigGlobalDO> list = vbConfigGlobalService.getVbConfigGlobalList(listReqVO);
        return success(BeanUtils.toBean(list, VbConfigGlobalRespVO.class));
    }
}
