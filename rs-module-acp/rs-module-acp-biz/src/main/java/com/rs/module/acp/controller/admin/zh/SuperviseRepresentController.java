package com.rs.module.acp.controller.admin.zh;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRepresentRespVO;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRepresentSaveReqVO;
import com.rs.module.acp.service.zh.SuperviseRepresentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-督导申诉")
@RestController
@RequestMapping("/acp/zh/superviseRepresent")
@Validated
public class SuperviseRepresentController {

    @Resource
    private SuperviseRepresentService superviseRepresentService;

    @PostMapping("/createRepresent")
    @ApiOperation(value = "创建督导申诉")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> createRepresent(@Valid @RequestBody SuperviseRepresentSaveReqVO createReqVO) {
        try  {
            return success(superviseRepresentService.createSuperviseRepresent(createReqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/approve")
    @ApiOperation(value = "督导申诉审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "督导id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "审批结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comments", value = "审批意见", required = true, dataType = "String")
    })
    public CommonResult<Boolean> approve(@Valid @RequestBody JSONObject approveReqVO) {
        try  {
            return success(superviseRepresentService.approve(approveReqVO));
        }catch (Exception e){
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

    @GetMapping("/getRepresent")
    @ApiOperation(value = "获取督导申诉信息")
    public CommonResult<SuperviseRepresentRespVO> getRepresent(@RequestParam("superviseId") String superviseId) {
        return success(superviseRepresentService.getSuperviseRepresent(superviseId));
    }
}
