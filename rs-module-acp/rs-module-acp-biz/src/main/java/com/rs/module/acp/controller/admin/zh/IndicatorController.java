package com.rs.module.acp.controller.admin.zh;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.IndicatorDO;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-绩效考核指标")
@RestController
@RequestMapping("/acp/zh/indicator")
@Validated
public class IndicatorController {

    @Resource
    private IndicatorService indicatorService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核指标")
    public CommonResult<String> createIndicator(@Valid @RequestBody IndicatorSaveReqVO createReqVO) {
        return success(indicatorService.createIndicator(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核指标")
    public CommonResult<Boolean> updateIndicator(@Valid @RequestBody IndicatorSaveReqVO updateReqVO) {
        indicatorService.updateIndicator(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核指标")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteIndicator(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorService.deleteIndicator(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核指标")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorRespVO> getIndicator(@RequestParam("id") String id) {
        IndicatorRespVO indicator = indicatorService.getIndicator(id);
        return success(indicator);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核指标分页")
    public CommonResult<PageResult<IndicatorRespVO>> getIndicatorPage(@Valid @RequestBody IndicatorPageReqVO pageReqVO) {
        PageResult<IndicatorDO> pageResult = indicatorService.getIndicatorPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndicatorRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核指标列表")
    public CommonResult<List<IndicatorRespVO>> getIndicatorList(@Valid @RequestBody IndicatorListReqVO listReqVO) {
        if(StringUtils.isEmpty(listReqVO.getOrgCode())){
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        return success(indicatorService.getIndicatorList(listReqVO));
    }

    @PostMapping("/startStopHandle")
    @ApiOperation(value = "绩效考核指标停启用")
    public CommonResult<Boolean> startStopHandle(@Valid @RequestBody IndicatorStartStopHandleReqVO updateReqVO) {
        indicatorService.startStopHandle(updateReqVO);
        return success(true);
    }

}
