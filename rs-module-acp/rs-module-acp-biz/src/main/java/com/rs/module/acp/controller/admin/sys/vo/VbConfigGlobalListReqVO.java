package com.rs.module.acp.controller.admin.sys.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-语音播报-全局配置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigGlobalListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("播报静音时段，格式：00:00:00-10:00:00")
    private String silentTimeSlots;

}
