package com.rs.module.acp.controller.admin.zh.vo.shiftteam;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 值班模板班次 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffDutyShiftRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("模板ID")
    private String tempId;
    @ApiModelProperty("班次名称")
    private String shiftName;
    @ApiModelProperty("班次顺序")
    private Integer shiftOrder;
}
