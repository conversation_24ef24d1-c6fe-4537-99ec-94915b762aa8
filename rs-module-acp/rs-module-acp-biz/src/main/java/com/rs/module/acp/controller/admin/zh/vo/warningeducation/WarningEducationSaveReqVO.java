package com.rs.module.acp.controller.admin.zh.vo.warningeducation;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-警示教育新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningEducationSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("警示人（民警）身份证号")
    @NotEmpty(message = "警示人（民警）身份证号不能为空")
    private String warningPoliceSfzh;

    @ApiModelProperty("警示人（民警）姓名")
    @NotEmpty(message = "警示人（民警）姓名不能为空")
    private String warningPoliceName;

    @ApiModelProperty("违规类型")
    private String violationType;

    @ApiModelProperty("违规情况详情")
    @NotEmpty(message = "违规情况详情不能为空")
    private String violationSituation;

    @ApiModelProperty("登记人身份证号")
    @NotEmpty(message = "登记人身份证号不能为空")
    private String operatorSfzh;

    @ApiModelProperty("操作人姓名")
    @NotEmpty(message = "操作人姓名不能为空")
    private String operatorName;

    @ApiModelProperty("operator_time")
    @NotNull(message = "operator_time不能为空")
    private Date operatorTime;

}
