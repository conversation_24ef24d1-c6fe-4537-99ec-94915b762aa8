package com.rs.module.acp.controller.admin.zh.vo.warningeducation;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 综合管理-警示教育关联人员 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WarningEducationPersonRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("警示教育ID")
    private String warningEducationId;
    @ApiModelProperty("警示人（民警）身份证号")
    private String warningPoliceSfzh;
    @ApiModelProperty("警示人（民警）姓名")
    private String warningPoliceName;
}
