package com.rs.module.acp.controller.admin.pi.vo.sqgl;

import com.fhs.core.trans.vo.VO;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-巡视管控-所情管理-所情登记新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SqglSqdjSaveReqVO extends BaseVO implements VO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("所情编号")
    private String eventCode;

    @ApiModelProperty("所情等级，字典：")
    @NotEmpty(message = "所情等级，字典：不能为空")
    private String eventLevel;

    @ApiModelProperty("所情地点")
    @NotEmpty(message = "所情地点不能为空")
    private String areaId;

    @ApiModelProperty("所情地点名称")
    @NotEmpty(message = "所情地点名称不能为空")
    private String areaName;

    @ApiModelProperty("发生时间")
    @NotNull(message = "发生时间不能为空")
    private Date happenTime;

    @ApiModelProperty("event_template_id")
    private String eventTemplateId;

    @ApiModelProperty("所情名称")
    @NotEmpty(message = "所情名称不能为空")
    private String eventName;

    @ApiModelProperty("所情类型")
    @NotEmpty(message = "所情类型不能为空")
    private String eventType;

    @ApiModelProperty("所情开始时间（精确时间-开始）")
    @NotNull(message = "所情开始时间（精确时间-开始）不能为空")
    private Date eventStartTime;

    @ApiModelProperty("所情结束时间（精确时间-结束）")
    private Date eventEndTime;

    @ApiModelProperty("所情详情")
    private String eventDetails;

    @ApiModelProperty("处置情况")
    private String handleInfo;

    @ApiModelProperty("状态，字典：")
    private String status;

    @ApiModelProperty("所情来源")
    private String eventSrc;

    @ApiModelProperty("报警人员列表")
    private List<SqglSqdjGlrySaveReqVO> bjryList;

    @ApiModelProperty("在押人员列表")
    private List<SqglSqdjGlrySaveReqVO> jgryList;

    @ApiModelProperty("工作人员列表")
    private List<SqglSqdjGlrySaveReqVO> gzryList;

    @ApiModelProperty("外来人员列表")
    private List<SqglSqdjGlrySaveReqVO> wlryList;

    @ApiModelProperty("推送对象列表")
    private List<SqglSqdjtsdxSaveReqVO> tsdxList;

    @ApiModelProperty("所情核实-（1：提交，2：办结）")
    private String saveType = "1";

    @ApiModelProperty("告警类型，无需传值")
    private String alarmType;

    @ApiModelProperty("被监管人员编码，前端无需传值")
    private String bjgrybm;

    @ApiModelProperty("预警表ID，前端无需传值")
    private String alarmEventId;

    @ApiModelProperty("第三方设备ID（海康：存国标编码，广拓周界：存周界防区名称），前端无需传值")
    private String thirdDeviceId;

}
