package com.rs.module.acp.controller.enums;

/**
 * 所情核实状态枚举
 */
public enum SuoQingHeShiStatus {

    /**
     * 0 - 待处置：初始状态，等待处理
     */
    PENDING("0", "待处置"),

    /**
     * 1 - 中间环节的处置：正在中间环节中被处理
     */
    IN_PROGRESS("1", "中间环节的处置"),

    /**
     * 2 - 中间环节最后一个处理的平行节点：处于中间环节的最后一个并行处理节点
     */
    LAST_PARALLEL_NODE("2", "中间环节最后一个处理的平行节点"),

    /**
     * 3 - 无需处理：该事项不需要进行处理
     */
    NOT_REQUIRED("3", "无需处理"),

    /**
     * 4 - 误报
     */
    WB("4", "误报");

    private final String code;
    private final String description;

    SuoQingHeShiStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取状态码（字符串类型）
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据 code 获取对应的枚举值
     * @param code 状态码（字符串）
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果未找到匹配的枚举
     */
    public static SuoQingHeShiStatus fromCode(String code) {
        for (SuoQingHeShiStatus status : SuoQingHeShiStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的所情核实状态码: " + code);
    }

    /**
     * 安全的 fromCode，未找到时返回 null
     */
    public static SuoQingHeShiStatus fromCodeOrNull(String code) {
        if (code == null) {
            return null;
        }
        for (SuoQingHeShiStatus status : SuoQingHeShiStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "SuoQingHeShiStatus{" +
                "code='" + code + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
