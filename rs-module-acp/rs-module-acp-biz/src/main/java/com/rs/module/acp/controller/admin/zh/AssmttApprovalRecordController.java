package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.AssmttApprovalRecordSaveReqVO;
import com.rs.module.acp.entity.zh.AssmttApprovalRecordDO;
import com.rs.module.acp.service.zh.indicatorcate.AssmttApprovalRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-绩效考核-考核审核记录")
@RestController
@RequestMapping("/acp/zh/assmttApprovalRecord")
@Validated
public class AssmttApprovalRecordController {

    @Resource
    private AssmttApprovalRecordService assmttApprovalRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核-考核审核记录")
    public CommonResult<String> createAssmttApprovalRecord(@Valid @RequestBody AssmttApprovalRecordSaveReqVO createReqVO) {
        return success(assmttApprovalRecordService.createAssmttApprovalRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核-考核审核记录")
    public CommonResult<Boolean> updateAssmttApprovalRecord(@Valid @RequestBody AssmttApprovalRecordSaveReqVO updateReqVO) {
        assmttApprovalRecordService.updateAssmttApprovalRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核-考核审核记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAssmttApprovalRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           assmttApprovalRecordService.deleteAssmttApprovalRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核-考核审核记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AssmttApprovalRecordRespVO> getAssmttApprovalRecord(@RequestParam("id") String id) {
        AssmttApprovalRecordDO assmttApprovalRecord = assmttApprovalRecordService.getAssmttApprovalRecord(id);
        return success(BeanUtils.toBean(assmttApprovalRecord, AssmttApprovalRecordRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核-考核审核记录分页")
    public CommonResult<PageResult<AssmttApprovalRecordRespVO>> getAssmttApprovalRecordPage(@Valid @RequestBody AssmttApprovalRecordPageReqVO pageReqVO) {
        PageResult<AssmttApprovalRecordDO> pageResult = assmttApprovalRecordService.getAssmttApprovalRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssmttApprovalRecordRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核-考核审核记录列表")
    public CommonResult<List<AssmttApprovalRecordRespVO>> getAssmttApprovalRecordList(@Valid @RequestBody AssmttApprovalRecordListReqVO listReqVO) {
        List<AssmttApprovalRecordDO> list = assmttApprovalRecordService.getAssmttApprovalRecordList(listReqVO);
        return success(BeanUtils.toBean(list, AssmttApprovalRecordRespVO.class));
    }
}
