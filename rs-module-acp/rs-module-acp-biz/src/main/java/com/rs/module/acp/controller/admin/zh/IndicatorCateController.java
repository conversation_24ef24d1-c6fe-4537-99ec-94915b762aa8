package com.rs.module.acp.controller.admin.zh;

import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCatePageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateSaveReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateDO;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorCateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-绩效考核指标分类")
@RestController
@RequestMapping("/acp/zh/indicatorCate")
@Validated
public class IndicatorCateController {

    @Resource
    private IndicatorCateService indicatorCateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核指标分类")
    public CommonResult<String> createIndicatorCate(@Valid @RequestBody IndicatorCateSaveReqVO createReqVO) {
        return success(indicatorCateService.createIndicatorCate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核指标分类")
    public CommonResult<Boolean> updateIndicatorCate(@Valid @RequestBody IndicatorCateSaveReqVO updateReqVO) {
        indicatorCateService.updateIndicatorCate(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核指标分类")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteIndicatorCate(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorCateService.deleteIndicatorCate(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorCateRespVO> getIndicatorCate(@RequestParam("id") String id) {
        IndicatorCateRespVO indicatorCate = indicatorCateService.getIndicatorCate(id);
        return success(indicatorCate);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类分页")
    public CommonResult<PageResult<IndicatorCateRespVO>> getIndicatorCatePage(@Valid @RequestBody IndicatorCatePageReqVO pageReqVO) {
        if(StringUtils.isEmpty(pageReqVO.getOrgCode())){
            pageReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        PageResult<IndicatorCateDO> pageResult = indicatorCateService.getIndicatorCatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndicatorCateRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类列表")
    public CommonResult<List<IndicatorCateRespVO>> getIndicatorCateList(@Valid @RequestBody IndicatorCateListReqVO listReqVO) {
        if(StringUtils.isEmpty(listReqVO.getOrgCode())){
            listReqVO.setOrgCode(SessionUserUtil.getSessionUser().getOrgCode());
        }
        List<IndicatorCateDO> list = indicatorCateService.getIndicatorCateList(listReqVO);
        return success(BeanUtils.toBean(list, IndicatorCateRespVO.class));
    }
}
