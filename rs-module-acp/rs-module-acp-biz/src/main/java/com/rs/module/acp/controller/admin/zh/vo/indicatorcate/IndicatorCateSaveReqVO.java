package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCateSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("指标类型名称")
    @NotEmpty(message = "指标类型名称不能为空")
    private String typeName;

    @ApiModelProperty("初始化分值")
    @NotNull(message = "初始化分值不能为空")
    private BigDecimal initScore;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

    @ApiModelProperty("被考核对象类型，01:岗位、02：角色、03：用户  字典：ZD_JXKH_BKHDXLX")
    @NotNull(message = "被考核对象类型不能为空")
    private String assessedObjectType;

    @ApiModelProperty("被考核对象ID")
    private String assessedObjectId;

    @ApiModelProperty("被考核对象名称")
    private String assessedObjectName;

    @ApiModelProperty("排序序号")
    @NotNull(message = "排序序号不能为空")
    private Integer sortOrder;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("被考核对象")
    @NotNull(message = "被考核对象列表不能为空")
    private List<IndicatorCateAssessedSaveReqVO> assessedList;

    @ApiModelProperty("考核人")
    @NotNull(message = "考核人列表不能为空")
    private List<IndicatorCateAssessorSaveReqVO> assessorList;

}
