package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorListReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorPageReqVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorRespVO;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.IndicatorCateAssessorSaveReqVO;
import com.rs.module.acp.entity.zh.IndicatorCateAssessorDO;
import com.rs.module.acp.service.zh.indicatorcate.IndicatorCateAssessorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-绩效考核指标分类与考评人关联")
@RestController
@RequestMapping("/acp/zh/indicatorCateAssessor")
@Validated
public class IndicatorCateAssessorController {

    @Resource
    private IndicatorCateAssessorService indicatorCateAssessorService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核指标分类与考评人关联")
    public CommonResult<String> createIndicatorCateAssessor(@Valid @RequestBody IndicatorCateAssessorSaveReqVO createReqVO) {
        return success(indicatorCateAssessorService.createIndicatorCateAssessor(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核指标分类与考评人关联")
    public CommonResult<Boolean> updateIndicatorCateAssessor(@Valid @RequestBody IndicatorCateAssessorSaveReqVO updateReqVO) {
        indicatorCateAssessorService.updateIndicatorCateAssessor(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核指标分类与考评人关联")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteIndicatorCateAssessor(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           indicatorCateAssessorService.deleteIndicatorCateAssessor(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类与考评人关联")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<IndicatorCateAssessorRespVO> getIndicatorCateAssessor(@RequestParam("id") String id) {
        IndicatorCateAssessorDO indicatorCateAssessor = indicatorCateAssessorService.getIndicatorCateAssessor(id);
        return success(BeanUtils.toBean(indicatorCateAssessor, IndicatorCateAssessorRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类与考评人关联分页")
    public CommonResult<PageResult<IndicatorCateAssessorRespVO>> getIndicatorCateAssessorPage(@Valid @RequestBody IndicatorCateAssessorPageReqVO pageReqVO) {
        PageResult<IndicatorCateAssessorDO> pageResult = indicatorCateAssessorService.getIndicatorCateAssessorPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IndicatorCateAssessorRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核指标分类与考评人关联列表")
    public CommonResult<List<IndicatorCateAssessorRespVO>> getIndicatorCateAssessorList(@Valid @RequestBody IndicatorCateAssessorListReqVO listReqVO) {
        List<IndicatorCateAssessorDO> list = indicatorCateAssessorService.getIndicatorCateAssessorList(listReqVO);
        return success(BeanUtils.toBean(list, IndicatorCateAssessorRespVO.class));
    }
}
