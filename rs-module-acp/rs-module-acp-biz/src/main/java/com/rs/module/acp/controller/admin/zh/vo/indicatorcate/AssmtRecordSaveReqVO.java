package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-记录新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmtRecordSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("被考核人身份证号")
    @NotEmpty(message = "被考核人身份证号不能为空")
    private String assessedSfzh;

    @ApiModelProperty("被考核人姓名")
    @NotEmpty(message = "被考核人姓名不能为空")
    private String assessedName;

    @ApiModelProperty("所属岗位")
    private String postName;

    @ApiModelProperty("考核月份")
    @NotEmpty(message = "考核月份不能为空")
    private String assmtMonth;

    @ApiModelProperty("指标类型ID")
    @NotEmpty(message = "指标类型ID不能为空")
    private String indicatorCateId;

    @ApiModelProperty("指标类型名称")
    @NotEmpty(message = "指标类型名称不能为空")
    private String indicatorCateName;

    @ApiModelProperty("截止日期")
    @NotNull(message = "截止日期不能为空")
    private Date expiryDate;

    @ApiModelProperty("加减分得分")
    private BigDecimal addsubtractScore;

    @ApiModelProperty("主观得分")
    private BigDecimal subjectiveScore;

    @ApiModelProperty("总得分")
    private BigDecimal totalScore;

    @ApiModelProperty("状态")
    @NotEmpty(message = "状态不能为空")
    private String status;

}
