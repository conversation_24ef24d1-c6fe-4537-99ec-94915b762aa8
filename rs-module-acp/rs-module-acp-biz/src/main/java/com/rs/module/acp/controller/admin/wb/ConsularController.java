package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.ConsularPageReqVO;
import com.rs.module.acp.controller.admin.wb.vo.ConsularRespVO;
import com.rs.module.acp.controller.admin.wb.vo.ConsularSaveReqVO;
import com.rs.module.acp.entity.wb.ConsularDO;
import com.rs.module.acp.service.wb.ConsularService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-领事外事信息")
@RestController
@RequestMapping("/acp/wb/consular")
@Validated
public class ConsularController {

    @Resource
    private ConsularService consularService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建领事外事信息")
    @LogRecordAnnotation(bizModule = "acp:consular:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建领事外事信息",
            success = "实战平台-窗口业务-创建领事外事信息成功", fail = "实战平台-窗口业务-创建领事外事信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createConsular(@Valid @RequestBody ConsularSaveReqVO createReqVO) {
        return success(consularService.createConsular(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新领事外事信息")
    @LogRecordAnnotation(bizModule = "acp:consular:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新领事外事信息",
            success = "实战平台-窗口业务-更新领事外事信息成功", fail = "实战平台-窗口业务-更新领事外事信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateConsular(@Valid @RequestBody ConsularSaveReqVO updateReqVO) {
        consularService.updateConsular(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除领事外事信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:consular:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除领事外事信息",
            success = "实战平台-窗口业务-删除领事外事信息功", fail = "实战平台-窗口业务-删除领事外事信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteConsular(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           consularService.deleteConsular(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得领事外事信息")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:consular:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得领事外事信息",
            success = "实战平台-窗口业务-获得领事外事信息成功", fail = "实战平台-窗口业务-获得领事外事信息失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<ConsularRespVO> getConsular(@RequestParam("id") String id) {
        ConsularDO consular = consularService.getConsular(id);
        return success(BeanUtils.toBean(consular, ConsularRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "实战平台-窗口业务-获得领事外事信息分页")
    @LogRecordAnnotation(bizModule = "acp:consular:page", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得领事外事信息分页",
            success = "实战平台-窗口业务-获得领事外事信息分页成功", fail = "实战平台-窗口业务-获得领事外事信息分页失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#pageReqVO}}")
    public CommonResult<PageResult<ConsularRespVO>> getConsularPage(@Valid @RequestBody ConsularPageReqVO pageReqVO) {
        PageResult<ConsularDO> pageResult = consularService.getConsularPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ConsularRespVO.class));
    }
}
