package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.indicatorcate.*;
import com.rs.module.acp.entity.zh.AssmttApprovalDO;
import com.rs.module.acp.service.zh.indicatorcate.AssmttApprovalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-绩效考核-加减分考核审核")
@RestController
@RequestMapping("/acp/zh/assmttApproval")
@Validated
public class AssmttApprovalController {

    @Resource
    private AssmttApprovalService assmttApprovalService;

    @PostMapping("/create")
    @ApiOperation(value = "创建综合管理-绩效考核-加减分考核审核")
    public CommonResult<String> createAssmttApproval(@Valid @RequestBody AssmttApprovalSaveReqVO createReqVO) {
        return success(assmttApprovalService.createAssmttApproval(createReqVO));
    }

    @PostMapping("/zd/approval")
    @ApiOperation(value = "中队审核")
    public CommonResult<Boolean> zdApproval(@Valid @RequestBody AssmttZdldApprovalReqVO approvalReqVO) {
        assmttApprovalService.zdApproval(approvalReqVO);
        return success(true);
    }
    @PostMapping("/zw/approval")
    @ApiOperation(value = "政委审核")
    public CommonResult<Boolean> zwApproval(@Valid @RequestBody AssmttZwApprovalReqVO approvalReqVO) {
        assmttApprovalService.zwApproval(approvalReqVO);
        return success(true);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合管理-绩效考核-加减分考核审核")
    public CommonResult<Boolean> updateAssmttApproval(@Valid @RequestBody AssmttApprovalSaveReqVO updateReqVO) {
        assmttApprovalService.updateAssmttApproval(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除综合管理-绩效考核-加减分考核审核")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteAssmttApproval(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           assmttApprovalService.deleteAssmttApproval(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得综合管理-绩效考核-加减分考核审核")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<AssmttApprovalRespVO> getAssmttApproval(@RequestParam("id") String id) {
        AssmttApprovalDO assmttApproval = assmttApprovalService.getAssmttApproval(id);
        return success(BeanUtils.toBean(assmttApproval, AssmttApprovalRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得综合管理-绩效考核-加减分考核审核分页")
    public CommonResult<PageResult<AssmttApprovalRespVO>> getAssmttApprovalPage(@Valid @RequestBody AssmttApprovalPageReqVO pageReqVO) {
        PageResult<AssmttApprovalDO> pageResult = assmttApprovalService.getAssmttApprovalPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssmttApprovalRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得综合管理-绩效考核-加减分考核审核列表")
    public CommonResult<List<AssmttApprovalRespVO>> getAssmttApprovalList(@Valid @RequestBody AssmttApprovalListReqVO listReqVO) {
        List<AssmttApprovalDO> list = assmttApprovalService.getAssmttApprovalList(listReqVO);
        return success(BeanUtils.toBean(list, AssmttApprovalRespVO.class));
    }

    @GetMapping("/checkAssessor")
    @ApiOperation(value = "检查考核人和绩效类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "assmtRecordId", value = "考核记录id"),
            @ApiImplicitParam(name = "indicatorType", value = "指标类型")
    })
    public CommonResult<Boolean> checkAssessor(@RequestParam("assmtRecordId") String assmtRecordId,
                                               @RequestParam("indicatorType") String indicatorType) {
        assmttApprovalService.checkAssessor(assmtRecordId, indicatorType);
        return success(true);
    }

    @GetMapping("/getAssmtRecord")
    @ApiOperation(value = "获得综合管理-绩效考核详情")
    @ApiImplicitParam(name = "assmtRecordId", value = "考核记录id")
    public CommonResult<AssmtRecordRespVO> getAssmtRecord(@RequestParam("assmtRecordId") String assmtRecordId) {
        return success(assmttApprovalService.getAssmtRecord(assmtRecordId));
    }

}
