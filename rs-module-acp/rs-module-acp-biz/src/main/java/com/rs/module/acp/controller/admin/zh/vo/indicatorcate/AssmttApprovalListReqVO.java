package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-绩效考核-加减分考核审核列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AssmttApprovalListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("考核记录ID")
    private String assmtRecordId;

    @ApiModelProperty("被考核人身份证号")
    private String assessedSfzh;

    @ApiModelProperty("被考核人姓名")
    private String assessedName;

    @ApiModelProperty("考核月份")
    private String assmtMonth;

    @ApiModelProperty("指标类型，01：自观指标，02：加减分指标")
    private String indicatorType;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

    @ApiModelProperty("考核时间")
    private Date[] assessorTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;

    @ApiModelProperty("任务ID")
    private String taskId;

}
