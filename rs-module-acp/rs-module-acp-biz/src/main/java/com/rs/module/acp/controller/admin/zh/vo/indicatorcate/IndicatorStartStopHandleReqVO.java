package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorStartStopHandleReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @NotNull(message = "主键不能为空")
    private String id;

    @ApiModelProperty("是否启用 0 否 1 是 字典：ZD_TQ_TYPE")
    @NotNull(message = "是否启用不能为空")
    private Short isEnabled;

}
