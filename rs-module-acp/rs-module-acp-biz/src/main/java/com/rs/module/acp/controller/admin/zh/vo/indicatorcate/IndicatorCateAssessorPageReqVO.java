package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类与考评人关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndicatorCateAssessorPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标分类ID")
    private String indicatorCateId;

    @ApiModelProperty("考核人身份证号")
    private String assessorSfzh;

    @ApiModelProperty("考核人姓名")
    private String assessorName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
