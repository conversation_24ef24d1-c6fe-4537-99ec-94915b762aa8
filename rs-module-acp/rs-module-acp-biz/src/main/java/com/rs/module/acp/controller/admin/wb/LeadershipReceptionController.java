package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.LeadershipReceptionRespVO;
import com.rs.module.acp.controller.admin.wb.vo.LeadershipReceptionSaveReqVO;
import com.rs.module.acp.entity.wb.LeadershipReceptionDO;
import com.rs.module.acp.service.wb.LeadershipReceptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-所领导接待登记")
@RestController
@RequestMapping("/acp/wb/leadershipReception")
@Validated
public class LeadershipReceptionController {

    @Resource
    private LeadershipReceptionService leadershipReceptionService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建所领导接待登记")
    @LogRecordAnnotation(bizModule = "acp:leadershipReception:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建所领导接待登记",
            success = "实战平台-窗口业务-创建所领导接待登记成功", fail = "实战平台-窗口业务-创建所领导接待登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createLeadershipReception(@Valid @RequestBody LeadershipReceptionSaveReqVO createReqVO) {
        return success(leadershipReceptionService.createLeadershipReception(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新所领导接待登记")
    @LogRecordAnnotation(bizModule = "acp:leadershipReception:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新所领导接待登记",
            success = "实战平台-窗口业务-更新所领导接待登记成功", fail = "实战平台-窗口业务-更新所领导接待登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateLeadershipReception(@Valid @RequestBody LeadershipReceptionSaveReqVO updateReqVO) {
        leadershipReceptionService.updateLeadershipReception(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除所领导接待登记")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:leadershipReception:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除所领导接待登记",
            success = "实战平台-窗口业务-删除所领导接待登记成功", fail = "实战平台-窗口业务-删除所领导接待登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteLeadershipReception(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           leadershipReceptionService.deleteLeadershipReception(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得所领导接待登记")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:leadershipReception:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得所领导接待登记",
            success = "实战平台-窗口业务-获得所领导接待登记成功", fail = "实战平台-窗口业务-获得所领导接待登记失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#ids}}")
    public CommonResult<LeadershipReceptionRespVO> getLeadershipReception(@RequestParam("id") String id) {
        LeadershipReceptionDO leadershipReception = leadershipReceptionService.getLeadershipReception(id);
        return success(BeanUtils.toBean(leadershipReception, LeadershipReceptionRespVO.class));
    }
}
