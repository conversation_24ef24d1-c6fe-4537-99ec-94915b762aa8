package com.rs.module.acp.controller.admin.sys.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-系统基础配置链接 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BasicConfigUrlRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("系统信息表主键")
    private String systemId;
    @ApiModelProperty("url类型 1：插件链接 2：浏览器链接 3：外链链接")
    private String urlType;
    @ApiModelProperty("链接名称")
    private String urlName;
    @ApiModelProperty("链接地址")
    private String url;
    @ApiModelProperty("是否展示 0：否 1：是")
    private String isShow;
    @ApiModelProperty("排序")
    private Short sort;
}
