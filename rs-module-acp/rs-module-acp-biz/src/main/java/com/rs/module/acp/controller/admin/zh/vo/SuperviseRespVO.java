package com.rs.module.acp.controller.admin.zh.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "管理后台 - 综合管理-督导信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SuperviseRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("督导编码")
    private String superviseNo;
    @ApiModelProperty("违规单位id")
    private String unitId;
    @ApiModelProperty("违规单位名称")
    private String unitName;
    @ApiModelProperty("违规时间")
    private Date outlineTime;
    @ApiModelProperty("违规地点id")
    private String addressId;
    @ApiModelProperty("违规地点名称")
    private String addressName;
    @ApiModelProperty("巡查来源")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DD_XCLY")
    private String patrolSource;
    @ApiModelProperty("违规对象")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DD_WGDX")
    private String outlineObject;
    @ApiModelProperty("违规人员编号")
    private String outlinePeopleNo;
    @ApiModelProperty("违规人员姓名")
    private String outlinePeopleName;
    @ApiModelProperty("违规问题数")
    private Integer questionNum;
    @ApiModelProperty("拟加扣分值")
    private BigDecimal deductValue;
    @ApiModelProperty("违规详情")
    private String detail;
    @ApiModelProperty("巡查时间")
    private Date aroundTime;
    @ApiModelProperty("整改期限")
    private Date correctPeriod;
    @ApiModelProperty("登记人id")
    private String operateUserSfzh;
    @ApiModelProperty("登记人名称")
    private String operateUserName;
    @ApiModelProperty("登记时间")
    private Date operateTime;
    @ApiModelProperty("是否整改反馈")
    private Short isCorrect;
    @ApiModelProperty("督导状态(01待审批，02不同意，03退回，04待整改，05已申诉，06已反馈，07已完成)")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DD_STATUS")
    private String status;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("违规类型")
    @Trans(type = TransType.DICTIONARY, key = "ZD_DD_WGLX")
    private String outLineViolation;
    @ApiModelProperty("附件信息")
    private String attachment;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("督导问题")
    private String superviseQuestion;
    @ApiModelProperty("接收人身份证号")
    private String receiveUserSfzh;
    @ApiModelProperty("接收人姓名")
    private String receiveUserName;
    @ApiModelProperty("当前审批人身份证号")
    private String approvalUserSfzh;
    @ApiModelProperty("当前审批人姓名")
    private String approvalUserName;
}
