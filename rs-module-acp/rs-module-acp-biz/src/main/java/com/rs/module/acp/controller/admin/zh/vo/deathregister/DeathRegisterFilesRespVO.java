package com.rs.module.acp.controller.admin.zh.vo.deathregister;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "实战平台-综合管理-死亡登记文件 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeathRegisterFilesRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("对应id")
    private String deathRegisterId;
    @ApiModelProperty("文件名称")
    private String fileName;
    @ApiModelProperty("文件地址")
    private String fileUrl;
    @ApiModelProperty("文件后缀")
    private String fileSuffix;
    @ApiModelProperty("死亡鉴定文件类型")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZHGL_SWWJLX")
    private String deathAppraiseFileType;
}
