package com.rs.module.acp.controller.admin.wb.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-领事会见人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConsularMeetingPersonPageReqVO extends PageParam {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("领事会见ID")
    private String consularMeetingId;

    @ApiModelProperty("人员类型")
    private String rylx;

    @ApiModelProperty("警号")
    private String jh;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("证件类型")
    private String zjlx;

    @ApiModelProperty("证件号码")
    private String zjhm;

    @ApiModelProperty("办案单位代码")
    private String badwdm;

    @ApiModelProperty("办案单位名称")
    private String badwmc;

    @ApiModelProperty("联系方式")
    private String lxfs;

    @ApiModelProperty("性别")
    private String xb;

    @ApiModelProperty("照片存储url")
    private String zpUrl;

    @ApiModelProperty("工作证件url")
    private String gzzjUrl;

    @ApiModelProperty("国籍")
    private String gj;

    @ApiModelProperty("工作单位")
    private String workUnit;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
