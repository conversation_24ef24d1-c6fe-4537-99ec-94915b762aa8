package com.rs.module.acp.controller.admin.device;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.entity.pm.BaseDeviceInscreenDO;
import com.rs.module.acp.listener.admin.device.DeviceDataImportListener;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.acp.service.pm.BaseDeviceInscreenService;
import com.rs.module.base.controller.admin.pm.vo.device.*;
import com.rs.module.base.entity.pm.AreaDO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.entity.pm.DeviceData;
import com.rs.module.base.entity.pm.device.BaseDeviceDO;
import com.rs.module.base.enums.DeviceTypeEnum;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import com.rs.module.base.service.pm.AreaService;
import com.rs.module.base.service.pm.device.BaseDeviceService;
import com.rs.module.base.vo.RoomVideoVO;
import com.rs.module.base.vo.TreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-监管管理-设备信息")
@RestController
@RequestMapping("/acp/pm/baseDevice")
@Validated
@Slf4j
public class BaseDeviceController {

    @Resource
    private BaseDeviceService baseDeviceService;

    @Resource
    private AreaService areaService;

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;

    @Autowired
    private BaseDeviceInscreenService baseDeviceInscreenService;

    @Autowired
    private BaseDeviceCameraService baseDeviceCameraService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-监管管理-设备信息")
    public CommonResult<String> createBaseDevice(@Valid @RequestBody BaseDeviceSaveReqVO createReqVO) {
        return success(baseDeviceService.createBaseDevice(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-监管管理-设备信息")
    public CommonResult<Boolean> updateBaseDevice(@Valid @RequestBody BaseDeviceSaveReqVO updateReqVO) {
        baseDeviceService.updateBaseDevice(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-监管管理-设备信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteBaseDevice(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           baseDeviceService.deleteBaseDevice(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-监管管理-设备信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<BaseDeviceRespVO> getBaseDevice(@RequestParam(value = "id") String id) {
        BaseDeviceDO baseDevice = baseDeviceService.getBaseDevice(id);

        AreaDO areaDO = areaService.getArea(baseDevice.getAreaId());
        BaseDeviceRespVO baseDeviceRespVO = BeanUtils.toBean(baseDevice, BaseDeviceRespVO.class);
        if (areaDO != null) {
            baseDeviceRespVO.setAllParentId(areaDO.getAllParentId());
            baseDeviceRespVO.setAreaName(areaDO.getAreaName());
        }

        if (DeviceTypeEnum.INDOOR_TERMINAL.getCode().equals(baseDevice.getDeviceTypeId())
                || DeviceTypeEnum.SCREEN_OUTSIDE_THE_WAREHOUSE.getCode().equals(baseDevice.getDeviceTypeId())) {
            LambdaQueryWrapper<BaseDeviceInscreenDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(BaseDeviceInscreenDO::getId,BaseDeviceInscreenDO::getDeviceNum,BaseDeviceInscreenDO::getSerialNumber)
                            .eq(BaseDeviceInscreenDO::getDeviceId,id);
            List<BaseDeviceInscreenDO> inscreenDOList = baseDeviceInscreenService.list(lambdaQueryWrapper);
            if(CollectionUtil.isNotEmpty(inscreenDOList)){
                baseDeviceRespVO.setDeviceNum(inscreenDOList.get(0).getDeviceNum());
                baseDeviceRespVO.setSerialNumber(inscreenDOList.get(0).getSerialNumber());
            }
        }


        if(StrUtil.isNotBlank(baseDevice.getRoomId())){
            AreaPrisonRoomDO areaPrisonRoom = areaPrisonRoomService.getAreaPrisonRoom(baseDevice.getRoomId());
            if(areaPrisonRoom != null){
                baseDeviceRespVO.setRoomName(areaPrisonRoom.getRoomName());
            }
        }

        //关联设备调整
        if(StrUtil.isNotBlank(baseDevice.getRefDeviceId() )){
            List<BaseDeviceDO> list = baseDeviceService.list(new LambdaQueryWrapper<BaseDeviceDO>().in(BaseDeviceDO::getId, Arrays.asList(baseDevice.getRefDeviceId().split(","))));
            if(CollUtil.isNotEmpty(list)){
                baseDeviceRespVO.setRefDeviceName(list.stream().map(BaseDeviceDO::getDeviceName).collect(Collectors.joining(",")));
            }
        }

        if(DeviceTypeEnum.PERIMETER_CONTROL.getCode().equals(baseDevice.getDeviceTypeId())){
            //周界防区设备在库里面没有直接进行关联，是由区域与摄像头间接进行关联的
            //这里为了沿用设备管理管理摄像机的前端样式，这里将refDeviceId 替换掉
            if(ObjectUtil.isNotEmpty(baseDevice.getAreaId())){
                List<RoomVideoVO> roomVideoVOList = baseDeviceCameraService.roomVideo(baseDevice.getAreaId());
                if(CollectionUtil.isNotEmpty(roomVideoVOList)){
                    baseDeviceRespVO.setRefDeviceId(roomVideoVOList.stream().map(RoomVideoVO::getDeviceId).collect(Collectors.joining(",")));
                    baseDeviceRespVO.setRefDeviceName(roomVideoVOList.stream().map(RoomVideoVO::getDeviceName).collect(Collectors.joining(",")));
                }
            }
        }

        return success(baseDeviceRespVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-监管管理-设备信息分页")
    public CommonResult<PageResult<BaseDeviceRespVO>> getBaseDevicePage(@Valid @RequestBody BaseDevicePageReqVO pageReqVO) {
        PageResult<BaseDeviceDO> pageResult = baseDeviceService.getBaseDevicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BaseDeviceRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-监管管理-设备信息列表")
    public CommonResult<List<BaseDeviceRespVO>> getBaseDeviceList(@Valid @RequestBody BaseDeviceListReqVO listReqVO) {
        List<BaseDeviceDO> list = baseDeviceService.getBaseDeviceList(listReqVO);
        return success(BeanUtils.toBean(list, BaseDeviceRespVO.class));
    }

    @ApiOperation("设备导入模板下载")
    @RequestMapping(value = "exportModel", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportModel(HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("template/xlsx/sbglmb.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            // 设置响应头
            String fileName = "设备导入模板.xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 直接复制文件流到响应输出流
            IOUtils.copy(inputStream, response.getOutputStream());
        } catch (Exception e) {
            log.error("导出区域导入下载失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 导入设备信息数据
     * <AUTHOR>
     * @date 2025/5/21 11:08
     * @param [file]
     * @return com.rs.framework.common.pojo.CommonResult<java.lang.String>
     */
    @ApiOperation(value = "导入设备数据")
    @PostMapping(value = "/importDeviceData")
    public CommonResult<String> importDeviceData(@RequestParam("file") MultipartFile file) throws Exception {
        List<DeviceData> dataList = new ArrayList<>();
        DeviceDataImportListener listener = new DeviceDataImportListener(dataList);

        EasyExcel.read(file.getInputStream(), DeviceData.class, listener)
                .sheet(0)
                .headRowNumber(1)
                .doRead();

        // 处理导入的数据
        baseDeviceService.importDeviceData(dataList);

        return CommonResult.success("导入设备数据成功");
    }
    @PostMapping("/getDeviceTreeByArea")
    @ApiOperation(value = "设备管理-监所区域-设备树")
    public CommonResult<List<TreeNode>> getDeviceTreeByArea(@Valid @RequestBody BaseDeviceTreeReqVO treeReqVO) {
        List<TreeNode> result = baseDeviceService.getDeviceTreeByArea(treeReqVO);
        return success(result);
    }
}
