package com.rs.module.acp.controller.admin.zh;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseRespVO;
import com.rs.module.acp.controller.admin.zh.vo.SuperviseSaveReqVO;
import com.rs.module.acp.entity.zh.SuperviseDO;
import com.rs.module.acp.service.zh.SuperviseRepresentService;
import com.rs.module.acp.service.zh.SuperviseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "综合管理-督导信息")
@RestController
@RequestMapping("/acp/zh/supervise")
@Validated
public class SuperviseController {

    @Resource
    private SuperviseService superviseService;
    @Resource
    private SuperviseRepresentService superviseRepresentService;

    @PostMapping("/create")
    @ApiOperation(value = "创建督导信息")
    public CommonResult<String> createSupervise(@Valid @RequestBody SuperviseSaveReqVO createReqVO) {
        try  {
            return success(superviseService.createSupervise(createReqVO));
        }catch (Exception e){
            return error(e.getMessage());
        }
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "督导id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "审批结果", required = true, dataType = "String"),
            @ApiImplicitParam(name = "comments", value = "审批意见", required = true, dataType = "String")
    })
    public CommonResult<Boolean> approve(@Valid @RequestBody JSONObject approveReqVO) {
        try  {
            return success(superviseService.approve(approveReqVO));
        }catch (Exception e){
            e.printStackTrace();
            return error(e.getMessage());
        }
    }

//    @PostMapping("/affirm")
//    @ApiOperation(value = "督导确认")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "督导id", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "status", value = "确认结果", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "comments", value = "确认意见", required = true, dataType = "String")
//    })
//    public CommonResult<Boolean> affirm(@Valid @RequestBody JSONObject reqVO) {
//        try  {
//            return success(superviseService.affirm(reqVO));
//        }catch (Exception e){
//            e.printStackTrace();
//            return error(e.getMessage());
//        }
//    }

    @PostMapping("/update")
    @ApiOperation(value = "更新督导信息")
    public CommonResult<Boolean> updateSupervise(@Valid @RequestBody SuperviseSaveReqVO updateReqVO) {
        superviseService.updateSupervise(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除督导信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteSupervise(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           superviseService.deleteSupervise(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得督导信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<SuperviseRespVO> getSupervise(@RequestParam("id") String id) {
        SuperviseDO supervise = superviseService.getSupervise(id);
        return success(BeanUtils.toBean(supervise, SuperviseRespVO.class));
    }

}
