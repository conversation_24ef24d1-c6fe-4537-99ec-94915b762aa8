package com.rs.module.acp.controller.admin.zh.vo.shiftteam;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 值班模板班次新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffDutyShiftSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("模板ID")
   // @NotEmpty(message = "模板ID不能为空")
    private String tempId;
    @ApiModelProperty("班次名称")
    @NotEmpty(message = "班次名称不能为空")
    private String shiftName;

    @ApiModelProperty("班次顺序")
    @NotNull(message = "班次顺序不能为空")
    private Integer shiftOrder;
}
