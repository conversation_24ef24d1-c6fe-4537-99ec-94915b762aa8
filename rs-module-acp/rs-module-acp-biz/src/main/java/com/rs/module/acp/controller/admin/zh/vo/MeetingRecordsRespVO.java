package com.rs.module.acp.controller.admin.zh.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-综合管理-会议记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingRecordsRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键ID，32位UUID")
    private String id;
    @ApiModelProperty("会议开始时间")
    private Date meetingStartTime;
    @ApiModelProperty("会议结束时间")
    private Date meetingEndTime;
    @ApiModelProperty("会议分类 字典 ZD_JLS_HYLX")
    @Trans(type = TransType.DICTIONARY,key = "ZD_JLS_HYLX")
    private String categoryId;
    @ApiModelProperty("会议地点")
    private String location;
    @ApiModelProperty("会议主题")
    private String subject;
    @ApiModelProperty("会议内容")
    private String content;
    @ApiModelProperty("参会人员姓名")
    private String participants;
    @ApiModelProperty("参会人员身份证号")
    private String participantsSfzh;
    @ApiModelProperty("记录人")
    private String recorder;
    @ApiModelProperty("会议纪要")
    private String meetingSummary;
    @ApiModelProperty("会议结论")
    private String conclusion;
    @ApiModelProperty("上传附件路径")
    private String attachment;
    @ApiModelProperty("文书号")
    private String wsh;
    @ApiModelProperty("文书字号")
    private String wszh;
    @ApiModelProperty("被监管人员编码,多个逗号分隔")
    private String jgrybms;
    @ApiModelProperty("被监管人员姓名,多个逗号分隔")
    private String jgryxms;
}
