package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoLargeScreenRespVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoRespVO;
import com.rs.module.acp.controller.admin.wb.vo.HallInfoSaveReqVO;
import com.rs.module.acp.service.wb.HallInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-服务大厅信息发布")
@RestController
@RequestMapping("/acp/wb/hallInfo")
@Validated
public class HallInfoController {

    @Resource
    private HallInfoService hallInfoService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建服务大厅信息发布")
    @LogRecordAnnotation(bizModule = "acp:hallInfo:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建服务大厅信息发布",
            success = "实战平台-窗口业务-创建服务大厅信息发布成功", fail = "实战平台-窗口业务-创建服务大厅信息发布失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createHallInfo(@Valid @RequestBody HallInfoSaveReqVO createReqVO) {
        return success(hallInfoService.createHallInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新服务大厅信息发布")
    @LogRecordAnnotation(bizModule = "acp:hallInfo:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新服务大厅信息发布",
            success = "实战平台-窗口业务-更新服务大厅信息发布成功", fail = "实战平台-窗口业务-更新服务大厅信息发布失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateHallInfo(@Valid @RequestBody HallInfoSaveReqVO updateReqVO) {
        hallInfoService.updateHallInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除服务大厅信息发布")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:hallInfo:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除服务大厅信息发布",
            success = "实战平台-窗口业务-删除服务大厅信息发布成功", fail = "实战平台-窗口业务-删除服务大厅信息发布失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteHallInfo(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           hallInfoService.deleteHallInfo(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得服务大厅信息发布详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:hallInfo:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得服务大厅信息发布详情",
            success = "实战平台-窗口业务-获得服务大厅信息发布详情成功", fail = "实战平台-窗口业务-获得服务大厅信息发布详情失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<HallInfoRespVO> getHallInfo(@RequestParam("id") String id) {
        return success(hallInfoService.getHallInfo(id));
    }

    @PostMapping("/getHallInfoForLargeScreen")
    @ApiOperation(value = "实战平台-窗口业务-实战平台-窗口业务-获取大屏展示内容")
    @LogRecordAnnotation(bizModule = "acp:hallInfo:getHallInfoForLargeScreen", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-实战平台-窗口业务-获取大屏展示内容",
            success = "实战平台-窗口业务-实战平台-窗口业务-获取大屏展示内容成功", fail = "实战平台-窗口业务-实战平台-窗口业务-获取大屏展示内容失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#listReqVO}}")
    public CommonResult<HallInfoLargeScreenRespVO> getHallInfoList() {
        return success(hallInfoService.getHallInfoForLargeScreen());
    }

    @GetMapping("/changeStatus")
    @ApiOperation(value = "实战平台-窗口业务-更改服务大厅信息发布状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "业务ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "status", value = "发布状态(0：可用，1：禁用)", required = true, dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:hallInfo:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-更改服务大厅信息发布状态",
            success = "实战平台-窗口业务-更改服务大厅信息发布状态成功", fail = "实战平台-窗口业务-更改服务大厅信息发布状态失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{TO_JSON{#params}}")
    public CommonResult<Boolean> changeStatus(@RequestParam("id") String id,@RequestParam("status") String status) {
        return success(hallInfoService.changeStatus(id,status));
    }
}
