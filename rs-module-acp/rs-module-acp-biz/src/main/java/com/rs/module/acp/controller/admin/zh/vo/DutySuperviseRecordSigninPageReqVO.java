package com.rs.module.acp.controller.admin.zh.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-值班管理-值班督导记录签至分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DutySuperviseRecordSigninPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("值班记录ID")
    private String dutySuperviseRecordId;

    @ApiModelProperty("民警编号")
    private String policeId;

    @ApiModelProperty("民警姓名")
    private String policeName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
