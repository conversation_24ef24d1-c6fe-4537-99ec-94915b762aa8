package com.rs.module.acp.controller.admin.sys.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-语音播报-即时播报对象 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbConfigCurrentObjectRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("配置Id")
    private String configId;
    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;
    @ApiModelProperty("播报监区Id")
    private String vbAreaId;
    @ApiModelProperty("播报监室Id")
    private String vbRoomId;
}
