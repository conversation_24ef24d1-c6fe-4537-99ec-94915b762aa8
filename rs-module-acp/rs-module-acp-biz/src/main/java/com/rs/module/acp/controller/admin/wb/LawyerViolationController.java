package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.wb.vo.LawyerViolationRespVO;
import com.rs.module.acp.controller.admin.wb.vo.LawyerViolationSaveReqVO;
import com.rs.module.acp.entity.wb.LawyerViolationDO;
import com.rs.module.acp.service.wb.LawyerViolationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-律师违规")
@RestController
@RequestMapping("/acp/wb/lawyerViolation")
@Validated
public class LawyerViolationController {

    @Resource
    private LawyerViolationService lawyerViolationService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建律师违规")
    @LogRecordAnnotation(bizModule = "acp:lawyerViolation:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建律师违规",
            success = "实战平台-窗口业务-创建律师违规成功", fail = "实战平台-窗口业务-创建律师违规失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createLawyerViolation(@Valid @RequestBody LawyerViolationSaveReqVO createReqVO) {
        return success(lawyerViolationService.createLawyerViolation(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新律师违规")
    @LogRecordAnnotation(bizModule = "acp:lawyerViolation:update", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-更新律师违规",
            success = "实战平台-窗口业务-更新律师违规成功", fail = "实战平台-窗口业务-更新律师违规失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateLawyerViolation(@Valid @RequestBody LawyerViolationSaveReqVO updateReqVO) {
        lawyerViolationService.updateLawyerViolation(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得律师违规详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:lawyerViolation:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得律师违规详情",
            success = "实战平台-窗口业务-获得律师违规详情成功", fail = "实战平台-窗口业务-获得律师违规详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<LawyerViolationRespVO> getLawyerViolation(@RequestParam("id") String id) {
        LawyerViolationDO lawyerViolation = lawyerViolationService.getLawyerViolation(id);
        return success(BeanUtils.toBean(lawyerViolation, LawyerViolationRespVO.class));
    }

    @GetMapping("/getlawyerViolationListByLawyerId")
    @ApiOperation(value = "实战平台-窗口业务-根据律师ID律师违规列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "jgrybm", value = "监管人员编码", required = true, dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "页码", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", required = false, paramType = "query", dataType = "int")
    })
    @LogRecordAnnotation(bizModule = "acp:lawyerViolation:getlawyerViolationListByLawyerId", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据律师ID律师违规列表",
            success = "实战平台-窗口业务-根据律师ID律师违规列表成功", fail = "实战平台-窗口业务-根据律师ID律师违规列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#lawyerId}}")
    public CommonResult<PageResult<LawyerViolationRespVO>> getLawyerViolationListByLawyerId(@RequestParam("lawyerId") String lawyerId,
                                                                                            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                                                            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return success(lawyerViolationService.getLawyerViolationListByLawyerId(lawyerId,pageNo,pageSize));
    }

}
