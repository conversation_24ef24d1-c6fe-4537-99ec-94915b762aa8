package com.rs.module.acp.controller.admin.zh.vo.shiftteam;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "管理后台 - 值班模板班组信息新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffDutyTeamSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("关联的班次ID")
    @NotEmpty(message = "关联的班次ID不能为空")
    private String shiftId;

    @ApiModelProperty("班组名称")
    @NotEmpty(message = "班组名称不能为空")
    private String teamName;

    @ApiModelProperty("班组顺序")
    private Integer teamOrder;

}
