package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyShiftRespVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyShiftSaveReqVO;
import com.rs.module.acp.entity.zh.StaffDutyShiftDO;
import com.rs.module.acp.service.zh.shiftteam.StaffDutyShiftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班模板班次")
@RestController
@RequestMapping("/acp/zh/staffDutyShift")
@Validated
public class StaffDutyShiftController {

    @Resource
    private StaffDutyShiftService staffDutyShiftService;

    @PostMapping("/create")
    @ApiOperation(value = "创建值班模板班次")
    public CommonResult<String> createStaffDutyShift(@Valid @RequestBody StaffDutyShiftSaveReqVO createReqVO) {
        return success(staffDutyShiftService.createStaffDutyShift(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新值班模板班次")
    public CommonResult<Boolean> updateStaffDutyShift(@Valid @RequestBody StaffDutyShiftSaveReqVO updateReqVO) {
        staffDutyShiftService.updateStaffDutyShift(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除值班模板班次")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteStaffDutyShift(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           staffDutyShiftService.deleteStaffDutyShift(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得值班模板班次")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<StaffDutyShiftRespVO> getStaffDutyShift(@RequestParam("id") String id) {
        StaffDutyShiftDO staffDutyShift = staffDutyShiftService.getStaffDutyShift(id);
        return success(BeanUtils.toBean(staffDutyShift, StaffDutyShiftRespVO.class));
    }
    @GetMapping("/getListByOrgCode")
    @ApiOperation(value = "获得值班模板班组")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<List<StaffDutyShiftRespVO>> getListByOrgCode(@RequestParam(value = "orgCode",required = false) String orgCode) {
        List<StaffDutyShiftDO> list = staffDutyShiftService.getListByOrgCode(orgCode);
        return success(BeanUtils.toBean(list, StaffDutyShiftRespVO.class));
    }
}
