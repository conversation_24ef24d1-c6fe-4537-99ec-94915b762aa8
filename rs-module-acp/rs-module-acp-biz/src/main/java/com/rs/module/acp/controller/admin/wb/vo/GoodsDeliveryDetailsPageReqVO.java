package com.rs.module.acp.controller.admin.wb.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-物品顾送明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GoodsDeliveryDetailsPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("物品顾送ID")
    private String goodsDeliveryId;

    @ApiModelProperty("物品名称")
    private String goodsName;

    @ApiModelProperty("物品数量")
    private Integer goodsQuantity;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("物品备注")
    private String remark;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
