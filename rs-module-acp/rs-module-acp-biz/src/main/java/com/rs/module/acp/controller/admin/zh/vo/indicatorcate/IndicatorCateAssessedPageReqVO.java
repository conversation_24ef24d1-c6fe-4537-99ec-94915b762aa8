package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.rs.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标分类与被考评人关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndicatorCateAssessedPageReqVO extends PageParam{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标分类ID")
    private String indicatorCateId;

    @ApiModelProperty("被考核对象类型，01:岗位、02：角色、03：用户")
    private String assessedObjectType;

    @ApiModelProperty("assessed_object_id")
    private String assessedObjectId;

    @ApiModelProperty("assessed_object_name")
    private String assessedObjectName;

   @ApiModelProperty("排序属性")
   List<OrderItem> orderFields;
}
