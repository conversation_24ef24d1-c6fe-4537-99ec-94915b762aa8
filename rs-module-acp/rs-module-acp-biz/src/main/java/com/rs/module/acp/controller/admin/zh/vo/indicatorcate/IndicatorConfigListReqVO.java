package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 综合管理-绩效考核截止日期设置列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorConfigListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("截止日期类型，01：本月，02：下月")
    private String expiryDateType;

    @ApiModelProperty("间隔天数")
    private Integer intervalDays;

}
