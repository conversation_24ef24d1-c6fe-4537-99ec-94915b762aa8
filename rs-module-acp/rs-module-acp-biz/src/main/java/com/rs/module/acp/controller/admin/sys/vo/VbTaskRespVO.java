package com.rs.module.acp.controller.admin.sys.vo;

import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ApiModel(description = "管理后台 - 实战平台-语音播报-待播报任务 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class VbTaskRespVO extends BaseVO implements TransPojo{
    @ApiModelProperty("主键")
    private String id;
    @ApiModelProperty("播报监所Id")
    private String vbPrisonId;
    @ApiModelProperty("播报监区Id")
    private String vbAreaId;
    @ApiModelProperty("播报监室Id")
    private String vbRoomId;
    @ApiModelProperty("播报设备序列号")
    private String vbSerialNumber;
    @ApiModelProperty("播报类型(0:定时,1:实时)")
    private Short vbType;
    @ApiModelProperty("播报名称")
    private String vbName;
    @ApiModelProperty("播报内容")
    private String content;
    @ApiModelProperty("播报次数")
    private Short vbNum;
    @ApiModelProperty("优先级")
    private Short priority;
    @ApiModelProperty("播报开始时间")
    private Date startTime;
    @ApiModelProperty("播报结束时间")
    private Date endTime;
    @ApiModelProperty("状态(0:待播报,1:已播报,2:超时自动取消)")
    private Short status;
    @ApiModelProperty("播报时间")
    private Date vbTime;
    @ApiModelProperty("取消时间")
    private Date cancelTime;
}
