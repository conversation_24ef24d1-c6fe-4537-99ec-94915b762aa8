package com.rs.module.acp.controller.admin.zh.vo.deathregister;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.rs.framework.mybatis.entity.BaseVO;
import com.rs.module.acp.entity.zh.DeathRegisterFilesDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@ApiModel(description = "实战平台-综合管理-死亡登记 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeathRegisterRespVO extends BaseVO implements TransPojo{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("死亡地点")
    private String deathSite;
    @ApiModelProperty("死亡时间")
    private Date deathTime;
    @ApiModelProperty("现场处理情况")
    private String siteHandlingSituation;
    @ApiModelProperty("死亡人员姓名")
    private String jgryxm;
    @ApiModelProperty("死亡人员编号")
    private String jgrybm;
    @ApiModelProperty("是否需要死亡鉴定,0否、1是")
    private Short deathAppraise;
    @ApiModelProperty("当前节点 字典 ZD_ZHGL_SWGL")
    @Trans(type = TransType.DICTIONARY,key = "ZD_ZHGL_SWGL")
    private String status;
    @ApiModelProperty("死亡鉴定经办时间")
    private Date appraiseHandleTime;
    @ApiModelProperty("死亡鉴定经办人")
    private Integer appraiseHandleUser;
    @ApiModelProperty("死亡鉴定经办人名称")
    private String appraiseHandleUserName;
    @ApiModelProperty("处理情况")
    private String handleSituation;
    @ApiModelProperty("死亡鉴定单位代码")
    private String deathAppraiseUnitCode;
    @ApiModelProperty("死亡鉴定单位名称")
    private String deathAppraiseUnitName;
    @ApiModelProperty("尸体处理经办时间")
    private Date corpseHandleHandleTime;
    @ApiModelProperty("尸体处理经办人")
    private Integer corpseHandleHandleUser;
    @ApiModelProperty("尸体处理经办人名称")
    private String corpseHandleHandleUserName;
    @ApiModelProperty("尸体处理情况")
    private String corpseHandleSituation;
    @ApiModelProperty("尸体处理时间")
    private Date corpseHandleTime;
    @ApiModelProperty("审批人身份证号")
    private String approverSfzh;
    @ApiModelProperty("审批人姓名")
    private String approverXm;
    @ApiModelProperty("审批时间")
    private Date approverTime;
    @ApiModelProperty("审批结果")
    private String approvalResult;
    @ApiModelProperty("领导签名")
    private String approvalAutograph;
    @ApiModelProperty("领导签名日期")
    private Date approvalAutographTime;
    @ApiModelProperty("审核意见")
    private String approvalComments;
    @ApiModelProperty("ACT流程实例Id")
    private String actInstId;
    @ApiModelProperty("任务ID")
    private String taskId;
    @ApiModelProperty("文件列表")
    private List<DeathRegisterFilesDO> fileList;
}
