package com.rs.module.acp.controller.admin.wb;

import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsRespVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsSaveReqVO;
import com.rs.module.acp.controller.admin.wb.vo.SocialRelationsUpdateReqVO;
import com.rs.module.acp.service.wb.SocialRelationsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-社会关系")
@RestController
@RequestMapping("/acp/wb/socialRelations")
@Validated
public class SocialRelationsController {

    @Resource
    private SocialRelationsService socialRelationsService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建社会关系")
    @LogRecordAnnotation(bizModule = "acp:socialRelations:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建社会关系",
            success = "实战平台-窗口业务-创建社会关系成功", fail = "实战平台-窗口业务-创建社会关系失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<Boolean> createSocialRelations(@Valid @RequestBody SocialRelationsSaveReqVO createReqVO) {
        return success(socialRelationsService.createSocialRelations(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新社会关系")
    @LogRecordAnnotation(bizModule = "acp:socialRelations:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-更新社会关系",
            success = "实战平台-窗口业务-更新社会关系成功", fail = "实战平台-窗口业务-更新社会关系失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateSocialRelations(@Valid @RequestBody SocialRelationsUpdateReqVO updateReqVO) {
        socialRelationsService.updateSocialRelations(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除社会关系")
    @ApiImplicitParam(name = "ids", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:socialRelations:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除社会关系",
            success = "实战平台-窗口业务-删除社会关系成功", fail = "实战平台-窗口业务-删除社会关系失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteSocialRelations(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           socialRelationsService.deleteSocialRelations(id);
        }
        return success(true);
    }

    @GetMapping("/getSocialRelationsListByJgrybm")
    @ApiOperation(value = "实战平台-窗口业务-根据被监管人员编码获取社会关联列表")
    @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码")
    @LogRecordAnnotation(bizModule = "acp:socialRelations:getSocialRelationsListByJgrybm", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据被监管人员编码获取社会关联列表",
            success = "实战平台-窗口业务-根据被监管人员编码获取社会关联列表成功", fail = "实战平台-窗口业务-根据被监管人员编码获取社会关联列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#jgrybm}}")
    public CommonResult<List<SocialRelationsRespVO>> getSocialRelationsListByJgrybm(@RequestParam("jgrybm") String jgrybm) {
        return success(socialRelationsService.getSocialRelationsListByJgrybm(jgrybm));
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-根据ID获取社会关系详情")
    @ApiImplicitParam(name = "id", value = "业务ID")
    @LogRecordAnnotation(bizModule = "acp:socialRelations:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-根据ID获取社会关系详情",
            success = "实战平台-窗口业务-根据ID获取社会关系详情成功", fail = "实战平台-窗口业务-根据ID获取社会关系详情失败，错误信息：{{#_ret[msg]}}",
            extraInfo = "{{#id}}")
    public CommonResult<SocialRelationsRespVO> getLeadershipReception(@RequestParam("id") String id) {
        return success(socialRelationsService.getSocialRelationsById(id));
    }
}
