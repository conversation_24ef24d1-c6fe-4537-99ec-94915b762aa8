package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsRespVO;
import com.rs.module.acp.controller.admin.zh.vo.MeetingRecordsSaveReqVO;
import com.rs.module.acp.entity.zh.MeetingRecordsDO;
import com.rs.module.acp.service.zh.MeetingRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-综合管理-会议记录")
@RestController
@RequestMapping("/acp/zh/meetingRecords")
@Validated
public class MeetingRecordsController {

    @Resource
    private MeetingRecordsService meetingRecordsService;

    @PostMapping("/create")
    @ApiOperation(value = "创建实战平台-综合管理-会议记录")
    public CommonResult<String> createMeetingRecords(@Valid @RequestBody MeetingRecordsSaveReqVO createReqVO) {
        return success(meetingRecordsService.createMeetingRecords(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实战平台-综合管理-会议记录")
    public CommonResult<Boolean> updateMeetingRecords(@Valid @RequestBody MeetingRecordsSaveReqVO updateReqVO) {
        meetingRecordsService.updateMeetingRecords(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除实战平台-综合管理-会议记录")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteMeetingRecords(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           meetingRecordsService.deleteMeetingRecords(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得实战平台-综合管理-会议记录")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MeetingRecordsRespVO> getMeetingRecords(@RequestParam("id") String id) {
        MeetingRecordsDO meetingRecords = meetingRecordsService.getMeetingRecords(id);
        return success(BeanUtils.toBean(meetingRecords, MeetingRecordsRespVO.class));
    }

    @GetMapping("/getListMeetingRecordsByCategoryId")
    @ApiOperation(value = "查询指定会议类型及人员编码的会议记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "会议分类(01:减刑,02:假释,03:暂缓,04:支部党建,05:小组党建,06:所情,07:所级队伍,08:队级队伍,09:犯情,10:联席,11:其他)", required = true, dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码", dataTypeClass = String.class, paramType = "query")
    })
    public CommonResult<List<MeetingRecordsRespVO>> getListMeetingRecordsByCategoryId(@RequestParam("categoryId") String categoryId,
                                                                                      @RequestParam(name = "jgrybm", required = false) String jgrybm) {
        List<MeetingRecordsDO> list = meetingRecordsService.getListMeetingRecordsByCategoryId(categoryId,jgrybm);
        return success(BeanUtils.toBean(list, MeetingRecordsRespVO.class));
    }
}
