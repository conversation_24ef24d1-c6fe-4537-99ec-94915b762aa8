package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("指标类型ID")
    private String indicatorCateId;

    @ApiModelProperty("指标类型名称")
    private String indicatorCateName;

    @ApiModelProperty("指标类型，01：自观指标，02：加减分指标")
    private String indicatorType;

    @ApiModelProperty("排序序号")
    private Integer sortOrder;

    @ApiModelProperty("是否启用")
    private Short isEnabled;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("机构编码")
    private String orgCode;

}
