package com.rs.module.acp.controller.admin.wb;

import com.alibaba.fastjson.JSONObject;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.acp.service.wb.WbHomeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-窗口业务-大屏统计业务")
@RestController
@RequestMapping("/acp/wb/bigScreen")
@Validated
public class WbBigScreenController {


    @Autowired
    private WbHomeService wbHomeService;

    @GetMapping("/getMeetingMumStatistics")
    @ApiOperation(value = "实战平台-提押岗大屏-获取会见数量统计")
    @LogRecordAnnotation(bizModule = "acp:bigScreen:getMeetingMumStatistics", operateType = LogOperateType.QUERY,
            title = "实战平台-提押岗大屏-获取会见数量统计",success = "实战平台-提押岗大屏-获取会见数量统计成功",
            fail = "实战平台-提押岗大屏-获取会见数量统计失败，错误信息：{{#_ret[msg]}}", extraInfo = "")
    public CommonResult<List<JSONObject>> getMeetingMumStatistics() {
        return success(wbHomeService.getMeetingMumStatistics());
    }

    @GetMapping("/getToDayMeetingList")
    @ApiOperation(value = "实战平台-提押岗大屏-获取今日会见列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tczt", value = "提出状态（all：全部,dtc:待提出，dth:待提回）", required = false, dataType = "String"),
            @ApiImplicitParam(name = "ywlx", value = "业务类型（all:全部，0：提讯/询，1：提解，2：律师会见，3：家属会见，4：使馆领事会见）", required = false, paramType = "query", dataType = "String")
    })
    @LogRecordAnnotation(bizModule = "acp:bigScreen:getToDayMeetingList", operateType = LogOperateType.QUERY,
            title = "实战平台-提押岗大屏-获取今日会见列表",success = "实战平台-提押岗大屏-获取今日会见列表成功",
            fail = "实战平台-提押岗大屏-获取今日会见列表失败，错误信息：{{#_ret[msg]}}", extraInfo = "")
    public CommonResult<List<JSONObject>> getToDayMeetingList(@RequestParam(value = "tczt",defaultValue = "all",required = false) String tczt,
                                                              @RequestParam(value = "ywlx",defaultValue = "all",required = false) String ywlx) {
        return success(wbHomeService.getToDayMeetingList(tczt,ywlx));
    }
}
