package com.rs.module.acp.controller.admin.pi;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fhs.trans.service.impl.TransService;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.config.WebSocketServer;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.entity.pi.SqglBjldszDO;
import com.rs.module.acp.entity.pi.SqglSqdjDO;
import com.rs.module.acp.entity.pi.SqglSqdjtsdxDO;
import com.rs.module.acp.service.pi.SqglBjldszService;
import com.rs.module.acp.service.pi.SqglSqdjService;
import com.rs.module.acp.service.pi.SqglSqdjtsdxService;
import com.rs.module.acp.service.pm.BaseDeviceCameraService;
import com.rs.module.base.vo.RoomVideoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "实战平台-巡视管控-所情管理-所情登记")
@RestController
@RequestMapping("/acp/pi/sqglSqdj")
@Validated
@Log4j2
public class SqglSqdjController {

    @Resource
    private SqglSqdjService sqglSqdjService;
    @Resource
    private SqglBjldszService sqglBjldszService;
    @Resource
    private BaseDeviceCameraService baseDeviceCameraService;
    @Resource
    private SqglSqdjtsdxService sqglSqdjtsdxService;
    @Resource
    private TransService transService;

    @PostMapping("/create")
    @ApiOperation(value = "所情登记")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:create", operateType = LogOperateType.CREATE, title = "创建实战平台-巡视管控-所情管理-所情登记",
            success = "创建实战平台-巡视管控-所情管理-所情登记成功",
            fail = "创建实战平台-巡视管控-所情管理-所情登记失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createSqglSqdj(@Valid @RequestBody SqglSqdjSaveReqVO createReqVO) {
        return success(sqglSqdjService.createSqglSqdj(createReqVO));
    }

    @PostMapping("/verify")
    @ApiOperation(value = "所情核实")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:verify", operateType = LogOperateType.UPDATE, title = "创建实战平台-巡视管控-所情管理-所情核实",
            success = "创建实战平台-巡视管控-所情管理-所情核实成功",
            fail = "创建实战平台-巡视管控-所情管理-所情核实失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> verify(@Valid @RequestBody SqglSqdjSaveReqVO updateReqVO) {
        return success(sqglSqdjService.verify(updateReqVO));
    }
    @PostMapping("/v2/verify")
    @ApiOperation(value = "所情核实")
    public CommonResult<Boolean> v2verify(@Valid @RequestBody SqglSqdjSaveReqVO updateReqVO) {
        return success(sqglSqdjService.verify(updateReqVO));
    }

    @GetMapping("/noActionRequired")
    @ApiOperation(value = "无需处置")
    public CommonResult<Boolean> noActionRequired(@RequestParam("id") String id) {
        return success(sqglSqdjService.noActionRequired(id));
    }

    @PostMapping("/dispose")
    @ApiOperation(value = "所情处置")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:dispose", operateType = LogOperateType.UPDATE, title = "创建实战平台-巡视管控-所情管理-所情处置",
            success = "创建实战平台-巡视管控-所情管理-所情处置成功",
            fail = "创建实战平台-巡视管控-所情管理-所情处置失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> dispose(@Valid @RequestBody SqglSqczSaveReqVO updateReqVO) {
        return success(sqglSqdjService.dispose(updateReqVO));
    }

    @PostMapping("/approve")
    @ApiOperation(value = "领导审批")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:dispose", operateType = LogOperateType.UPDATE, title = "创建实战平台-巡视管控-所情管理-领导审批",
            success = "创建实战平台-巡视管控-所情管理-领导审批成功",
            fail = "创建实战平台-巡视管控-所情管理-领导审批失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> approve(@Valid @RequestBody SqglSqczApproveSaveReqVO updateReqVO) {
        return success(sqglSqdjService.approve(updateReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获取所情登记详情")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:get", operateType = LogOperateType.QUERY, title = "获取所情登记详情",
            success = "获取所情登记详情成功",
            fail = "获取所情登记详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<SqglSqdjRespVO> getSqglSqdj(@RequestParam("id") String id) {
        return success(sqglSqdjService.getSqglSqdjById(id));
    }

    @GetMapping("/getCurrent")
    @ApiOperation(value = "获取当前用户需办理的业务内容（所情处置、领导审批时使用）")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:sqglSqdj:getCurrent", operateType = LogOperateType.QUERY, title = "获取所情登记详情",
            success = "获取所情登记详情成功",
            fail = "获取所情登记详情失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<SqglSqczRespVO> getCurrent(@RequestParam("id") String id) {
        return success(sqglSqdjService.getCurrent(id));
    }

    @GetMapping("/test")
    @ApiOperation(value = "获取所情登记详情")
    public CommonResult<String> test() {
        //发送websocket消息到页面
        //报警联动设置-所情弹窗
        SqglBjldszDO sqglBjldszDO = sqglBjldszService.getById("1958374131242373120");
        SqglSqdjRespVO sqglSqdjRespVO = BeanUtils.toBean(sqglBjldszDO, SqglSqdjRespVO.class);
        transService.transOne(sqglSqdjRespVO);
        SqglSqdjDO sqglSqdj = sqglSqdjService.getById("1961364575622533120");
        if (ObjectUtil.isNotEmpty(sqglBjldszDO) && ObjectUtil.isNotEmpty(sqglBjldszDO.getOptionalLinkageSettings())
                && sqglBjldszDO.getOptionalLinkageSettings().contains("sqtc")) {
            log.info("满足WebSocket推送条件，开始发送实时消息");

            Map<String, Object> msg = BeanUtil.beanToMap(sqglSqdjRespVO);
            msg.put("businessType", "sqdj");
            msg.put("sqdjId", sqglSqdj.getId());
            msg.put("eventLevel", sqglSqdj.getEventLevel());
            msg.put("happenTime", DateUtil.format(sqglSqdj.getEventStartTime(), "yyyy-MM-dd HH:mm:ss"));
            msg.put("eventDetails", sqglSqdj.getEventDetails());
            msg.put("url", String.format("/discipline/institutionSituation/settle?id=%s", sqglSqdj.getId()));
            msg.put("status", sqglSqdj.getStatus());



            //关联实时视频
            if (sqglBjldszDO.getOptionalLinkageSettings().contains("sssp")) {
                List<RoomVideoVO> roomVideoVOS = baseDeviceCameraService.roomVideo("1100001130100030088");
                msg.put("roomVideo", roomVideoVOS);
            }

            try {
                List<SqglSqdjtsdxDO> sqglSqdjtsdxDOList = new ArrayList<>();
                SqglSqdjtsdxDO sqglSqdjtsdxDO = sqglSqdjtsdxService.getById("1961364576197152768");
                sqglSqdjtsdxDOList.add(sqglSqdjtsdxDO);
                // 直接从对象转为 JSONObject，保留 null 字段
                String jsonStr = JSONObject.toJSONString(msg, SerializerFeature.WriteMapNullValue);
                JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                send(jsonObject,sqglSqdjtsdxDOList);
                log.info("WebSocket消息发送成功，推送用户数量：{}", sqglSqdjtsdxDOList.size());
            } catch (Exception e) {
                log.error("WebSocket消息发送失败", e);
            }
        } else {
            log.debug("不满足WebSocket推送条件，跳过实时消息发送");
        }
        return success("测试成功");
    }

    /**
     * 发送消息到页面
     *
     * @param msg
     * @param userList
     */
    private void send(JSONObject msg, List<SqglSqdjtsdxDO> userList) {
        for (SqglSqdjtsdxDO txdx : userList) {
            try {
                WebSocketServer.sendInfo(msg.toJSONString(), txdx.getPushUserSfzh());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @GetMapping("/wb")
    @ApiOperation(value = "误报")
    public CommonResult<Boolean> wb(@RequestParam("id") String id) {
        return success(sqglSqdjService.wb(id));
    }
    @GetMapping("/ycz")
    @ApiOperation(value = "已处置")
    public CommonResult<Boolean> ycz(@RequestParam("id") String id) {
        return success(sqglSqdjService.ycz(id));
    }

    @PostMapping("/ts")
    @ApiOperation(value = "推送")
    public CommonResult<Boolean> ts(@Valid @RequestBody SqglSqdjTsSaveReqVO updateReqVO) {
        return success(sqglSqdjService.ts(updateReqVO));
    }

}
