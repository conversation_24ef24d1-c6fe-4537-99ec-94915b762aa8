package com.rs.module.acp.service.pi;

import com.bsp.common.orm.mybatis.service.IBaseService;
import com.rs.framework.common.pojo.PageResult;
import com.rs.module.acp.controller.admin.pi.vo.sqgl.*;
import com.rs.module.acp.entity.pi.SqglSqdjDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 实战平台-巡视管控-所情管理-所情登记 Service 接口
 *
 * <AUTHOR>
 */
public interface SqglSqdjService extends IBaseService<SqglSqdjDO>{

    /**
     * 创建实战平台-巡视管控-所情管理-所情登记
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createSqglSqdj(@Valid SqglSqdjSaveReqVO createReqVO);

    /**
     * 更新实战平台-巡视管控-所情管理-所情登记
     *
     * @param updateReqVO 更新信息
     */
    void updateSqglSqdj(@Valid SqglSqdjSaveReqVO updateReqVO);

    /**
     * 删除实战平台-巡视管控-所情管理-所情登记
     *
     * @param id 编号
     */
    void deleteSqglSqdj(String id);

    /**
     * 获得实战平台-巡视管控-所情管理-所情登记
     *
     * @param id 编号
     * @return 实战平台-巡视管控-所情管理-所情登记
     */
    SqglSqdjDO getSqglSqdj(String id);

    /**
    * 获得实战平台-巡视管控-所情管理-所情登记分页
    *
    * @param pageReqVO 分页查询
    * @return 实战平台-巡视管控-所情管理-所情登记分页
    */
    PageResult<SqglSqdjDO> getSqglSqdjPage(SqglSqdjPageReqVO pageReqVO);

    /**
    * 获得实战平台-巡视管控-所情管理-所情登记列表
    *
    * @param listReqVO 查询条件
    * @return 实战平台-巡视管控-所情管理-所情登记列表
    */
    List<SqglSqdjDO> getSqglSqdjList(SqglSqdjListReqVO listReqVO);


    /**
     * 所情核实
     * @param updateReqVO
     * @return
     */
    Boolean verify(SqglSqdjSaveReqVO updateReqVO);

    /**
     * 所情处置
     * @param updateReqVO
     * @return
     */
    Boolean dispose(SqglSqczSaveReqVO updateReqVO);


    /**
     * 领导审批
     * @param updateReqVO
     * @return
     */
    Boolean approve(SqglSqczApproveSaveReqVO updateReqVO);

    /**
     * 根据ID获取所请登记详情
     * @param id
     * @return
     */
    SqglSqdjRespVO getSqglSqdjById(String id);

    /**
     * 根据所情登记ID和当前用户信息，返回当前用户需要处置的内容
     * @param id
     * @return
     */
    SqglSqczRespVO getCurrent(String id);

    /**
     * 创建待核实的所情
     * @param createReqVO
     */
    void createWaitVerify(String orgCode,String orgName,SqglSqdjSaveReqVO createReqVO);

    /**
     * 无需处置
     * @param id
     * @return
     */
    boolean noActionRequired(String id);

    /**
     * 已处置
     * @param id
     * @return
     */
    boolean ycz(String id);
    /**
     * 误报
     * @param id
     * @return
     */
    boolean wb(String id);

    /**
     * 推送
     * @param sqglSqdjSaveReqVO
     * @return
     */
    boolean ts(SqglSqdjTsSaveReqVO sqglSqdjSaveReqVO);
}
