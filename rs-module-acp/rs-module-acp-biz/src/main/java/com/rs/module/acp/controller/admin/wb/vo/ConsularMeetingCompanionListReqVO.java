package com.rs.module.acp.controller.admin.wb.vo;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "管理后台 - 实战平台-窗口业务-领事外事会见同行登记列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsularMeetingCompanionListReqVO extends BaseVO {
private static final long serialVersionUID = 1L;
    @ApiModelProperty("领事会见ID")
    private String consularMeetingId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("证件号码")
    private String idCard;

    @ApiModelProperty("同行人类别")
    private String companionType;

    @ApiModelProperty("附件URL")
    private String attachmentUrl;

}
