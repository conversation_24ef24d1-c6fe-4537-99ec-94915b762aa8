package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamAndPersonRespVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamAndPersonSaveReqVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamRespVO;
import com.rs.module.acp.controller.admin.zh.vo.shiftteam.StaffDutyTeamSaveReqVO;
import com.rs.module.acp.entity.zh.StaffDutyTeamDO;
import com.rs.module.acp.service.zh.shiftteam.StaffDutyTeamService;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "值班模板班组信息")
@RestController
@RequestMapping("/acp/zh/staffDutyTeam")
@Validated
public class StaffDutyTeamController {

    @Resource
    private StaffDutyTeamService staffDutyTeamService;

    @PostMapping("/create")
    @ApiOperation(value = "创建值班模板班组信息")
    public CommonResult<String> createStaffDutyTeam(@Valid @RequestBody StaffDutyTeamSaveReqVO createReqVO) {
        return success(staffDutyTeamService.createStaffDutyTeam(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新值班模板班组信息")
    public CommonResult<Boolean> updateStaffDutyTeam(@Valid @RequestBody StaffDutyTeamSaveReqVO updateReqVO) {
        staffDutyTeamService.updateStaffDutyTeam(updateReqVO);
        return success(true);
    }
    @GetMapping("/delete")
    @ApiOperation(value = "删除值班模板班组信息")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteStaffDutyTeam(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           staffDutyTeamService.deleteStaffDutyTeam(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得值班模板班组信息")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<StaffDutyTeamRespVO> getStaffDutyTeam(@RequestParam("id") String id) {
        StaffDutyTeamDO staffDutyTeam = staffDutyTeamService.getStaffDutyTeam(id);
        return success(BeanUtils.toBean(staffDutyTeam, StaffDutyTeamRespVO.class));
    }
    @PostMapping("/saveTeamAndPerson")
    @ApiOperation(value = "保存值班模板班组及人员信息")
    public CommonResult<String> saveStaffDutyTeamAndPerson(@Valid @RequestBody StaffDutyTeamAndPersonSaveReqVO saveReqVO) {
        try {
            return success(staffDutyTeamService.saveStaffDutyTeamAndPerson(saveReqVO));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @PostMapping("/updateTeamAndPerson")
    @ApiOperation(value = "修改值班模板班组及人员信息")
    public CommonResult<Boolean> updateStaffDutyTeamAndPerson(@Valid @RequestBody StaffDutyTeamAndPersonSaveReqVO updateReqVO) {
        try {
            staffDutyTeamService.saveStaffDutyTeamAndPerson(updateReqVO);
            return success(true);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @GetMapping("/getTeamAndPerson")
    @ApiOperation(value = "获取值班模板班组及人员信息")
    @ApiImplicitParam(name = "id", value = "值班模板班组ID", required = true)
    public CommonResult<StaffDutyTeamAndPersonRespVO> getStaffDutyTeamAndPerson(@RequestParam("id") String id) {
        try {
            StaffDutyTeamAndPersonRespVO respVO = staffDutyTeamService.getStaffDutyTeamAndPerson(id,  StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
            return success(respVO);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
    @GetMapping("/getListByOrgCode")
    @ApiOperation(value = "获得模板班次")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<List<StaffDutyTeamRespVO>> getListByOrgCode(@RequestParam(value = "orgCode",required = false) String orgCode,
                                                                    @RequestParam("shiftId") String shiftId) {
        List<StaffDutyTeamDO> list = staffDutyTeamService.getListByOrgCode(orgCode,shiftId);
        return success(BeanUtils.toBean(list, StaffDutyTeamRespVO.class));
    }
    @GetMapping("/shiftTeamIndex")
    @ApiOperation(value = "班组班次首页")
    @ApiImplicitParam(name = "orgCode", value = "机构编号")
    public CommonResult<DutyManageVO> shiftTeamIndex(@RequestParam(value = "orgCode",required = false) String orgCode){
        DutyManageVO dutyManageVO = staffDutyTeamService.shiftTeamIndex(orgCode);
        return success(BeanUtils.toBean(dutyManageVO, DutyManageVO.class));
    }
    @GetMapping("/getTeamAndPersonByShiftId")
    @ApiOperation(value = "根据班次ID获取值班模板班组及人员信息")
    @ApiImplicitParam(name = "shiftId", value = "值班模板班次ID", required = true)
    public CommonResult<List<StaffDutyTeamAndPersonRespVO>> getStaffDutyTeamAndPerson(@RequestParam("shiftId")String shiftId,@RequestParam(value = "orgCode",required = false)String orgCode ) {
        try {
            List<StaffDutyTeamAndPersonRespVO> respVO = staffDutyTeamService.getStaffDutyTeamAndPersonByShiftId(shiftId,  orgCode,StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
            return success(respVO);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
