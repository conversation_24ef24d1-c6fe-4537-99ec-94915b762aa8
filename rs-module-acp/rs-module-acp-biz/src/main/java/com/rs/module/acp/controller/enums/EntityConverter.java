package com.rs.module.acp.controller.enums;

import com.alibaba.fastjson.JSONObject;
import com.rs.framework.common.exception.ServiceException;

public class EntityConverter {
    public static <T> T convertToEntity(DetainRegKssDataTypeEnum type, JSONObject json) {
        try {
            Class<?> clazz = Class.forName(type.getClassPath());
            return (T) JSONObject.parseObject(json.toJSONString(), clazz);
        } catch (ClassNotFoundException e) {
            throw new ServiceException("Class not found: " + e.getMessage());
        } catch (Exception e) {
            throw new ServiceException("Conversion failed", e);
        }
    }
}
