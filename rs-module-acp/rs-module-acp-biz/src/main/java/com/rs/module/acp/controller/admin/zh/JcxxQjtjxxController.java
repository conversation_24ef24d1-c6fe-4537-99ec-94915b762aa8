package com.rs.module.acp.controller.admin.zh;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.base.controller.admin.pm.vo.AreaPrisonRoomQjxxSaveReqVO;
import com.rs.module.base.entity.pm.AreaPrisonRoomDO;
import com.rs.module.base.service.pm.AreaPrisonRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "实战平台-综合管理-基础信息-监室起居条件信息")
@RestController
@RequestMapping("/api/jcxxqjtjxx")
@RequiredArgsConstructor
public class JcxxQjtjxxController {

    @Resource
    private AreaPrisonRoomService areaPrisonRoomService;
    @PostMapping("/save")
    @ApiOperation("监室起居条件信息保存")
    public CommonResult<String> deathRegisterSave(@RequestBody @Valid AreaPrisonRoomQjxxSaveReqVO reqVO) {
        AreaPrisonRoomDO entity = BeanUtils.toBean(reqVO, AreaPrisonRoomDO.class);
        boolean result = areaPrisonRoomService.updateById(entity);
        if(result){
            return CommonResult.success();
        }
        return CommonResult.error("监室起居条件信息保存出错！");
    }

    @PostMapping("/batchSave")
    @ApiOperation("监室起居条件信息保存")
    public CommonResult<String> batchSave(@RequestBody @Valid List<AreaPrisonRoomQjxxSaveReqVO> reqVO) {
        List<AreaPrisonRoomDO> entityList = BeanUtils.toBean(reqVO, AreaPrisonRoomDO.class);
        boolean result = areaPrisonRoomService.updateBatchById(entityList);
        if(result){
            return CommonResult.success();
        }
        return CommonResult.error("监室起居条件信息保存出错！");
    }
}

