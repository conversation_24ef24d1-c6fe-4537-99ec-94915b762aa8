package com.rs.module.acp.controller.admin.zh.vo.indicatorcate;

import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "管理后台 - 综合管理-绩效考核指标新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorSaveReqVO extends BaseVO{
private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("指标名称")
    @NotEmpty(message = "指标名称不能为空")
    private String indicatorName;

    @ApiModelProperty("指标类型ID")
    @NotEmpty(message = "指标类型ID不能为空")
    private String indicatorCateId;

    @ApiModelProperty("指标类型名称")
    @NotEmpty(message = "指标类型名称不能为空")
    private String indicatorCateName;

    @ApiModelProperty("指标类型，01：主观分指标，02：加减分指标  字典：ZD_JXKH_ZBLX")
    private String indicatorType;

    @ApiModelProperty("排序序号")
    @NotNull(message = "排序序号不能为空")
    private Integer sortOrder;

    @ApiModelProperty("是否启用 0 否 1 是 字典：ZD_TQ_TYPE")
    @NotNull(message = "是否启用不能为空")
    private Short isEnabled;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("子指标")
    @NotNull(message = "子指标不能为空")
    private List<IndicatorSubSaveReqVO>  subList;

}
