package com.rs.module.acp.controller.admin.zh;

import com.alibaba.fastjson.JSONObject;
import com.bsp.common.util.DateUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.security.util.SessionUserUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageSaveDTO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.DutyManageVO;
import com.rs.module.base.controller.admin.zh.vo.staffduty.PostSynergismRespVO;
import com.rs.module.base.enums.StaffDutyTypeEnum;
import com.rs.module.base.service.zh.staffduty.StaffDutyRecordService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;

@Api(value = "实战平台-综合管理-值班安排", tags = "实战平台-综合管理-值班安排")
@Slf4j
@RequestMapping("/acp/zh/staffDutyRecord")
@RestController
@Validated
public class StaffDutyRecordController {

    @Autowired
    private StaffDutyRecordService staffDutyRecordService;

    @ApiOperation(value = "实战平台-综合管理-值班安排首页列表")
    @GetMapping(value = "/listByDutyDate")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "开始时间", dataType = "date"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "结束时间", dataType = "date")
    })
    public CommonResult<DutyManageVO> listByDutyDate(@RequestParam Date startTime, @RequestParam Date endTime) {
        return CommonResult.success(staffDutyRecordService.listByDutyDate(startTime, endTime, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
    }

    @ApiOperation(value = "实战平台-综合管理-值班安排首页默认启用状态表头")
    @GetMapping(value = "/defaultHeader")
    public CommonResult<DutyManageVO> defaultHeader() {
        return CommonResult.success(staffDutyRecordService.defaultHeader(StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
    }
    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    public CommonResult<String> add(@RequestBody DutyManageSaveDTO dto) {
        Integer result = staffDutyRecordService.add(dto);
        if (result > 0) {
            return CommonResult.success("新增成功");
        } else if (result == -1) {
            return CommonResult.error("已存在");
        } else {
            return CommonResult.error("新增失败");
        }
    }

  /*  @ApiOperation(value = "值班配置导出")
    @PostMapping(value = "/exportConfigByDutyDate")
    public void exportConfigByDutyDate(@RequestBody DutyManageSearchDTO manageSearchDTO, HttpServletResponse response) throws IOException {
        staffDutyRecordService.exportConfigByDutyDate(manageSearchDTO.getStartTime(), manageSearchDTO.getEndTime(), response);
    }*/

    @ApiOperation(value = "值班配置导入")
    @PostMapping(value = "/importConfigByDutyDate")
    public CommonResult<DutyManageVO> importConfigByDutyDate(@ApiParam(value = "Excel文件", required = true) @RequestParam("file") MultipartFile multipartFile) throws IOException {
        DutyManageVO vo = staffDutyRecordService.importConfigByDutyDate(multipartFile,StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode());
        Integer result = vo.getCode();
        if (result > 0) {
            return CommonResult.success(vo);
        } else if (result == -1) {
            return CommonResult.error("模板与当前系统模板不一致");
        } else if (result == -2) {
            return CommonResult.error("日期不能包含当天以前");
        } else if (result == -3) {
            return CommonResult.error("日期不能为空");
        } else if (result == -4) {
            return CommonResult.error("日期必修连续，不能间隔大于一天");
        } else {
            return CommonResult.error("系统异常");
        }
    }
    @GetMapping("/listByDate")
    @ApiOperation("获取单天值班安排")
    public CommonResult<DutyManageVO> listByDate(@RequestParam(required = false) String dutyDate) {
        try {
            //如果日期为空则获取当前日期
            if (StringUtil.isEmpty(dutyDate)) {
                dutyDate = DateUtil.getCurrentDate();
            }
            return CommonResult.success(staffDutyRecordService.listBySingleDutyDate(dutyDate, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
    @GetMapping("/listByDutySingleDateIndex")
    @ApiOperation("获取单天值班安排")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "监所编码，不传默认当前登录用户监所编码", required = false),
            @ApiImplicitParam(name = "dutyDate", dataType = "String", value = "不传默认当天", required = false)
    })
    public CommonResult<List<JSONObject>> listByDutySingleDateIndex(@RequestParam(required = false) String dutyDate, @RequestParam(required = false) String orgCode) {
        try {
            //如果日期为空则获取当前日期
            if (StringUtil.isEmpty(dutyDate)) {
                dutyDate = DateUtil.getCurrentDate();
            }
            if (StringUtil.isEmpty(orgCode)) {
                orgCode = SessionUserUtil.getSessionUser().getOrgCode();
            }
            return CommonResult.success(staffDutyRecordService.listByDutySingleDateIndex(dutyDate,orgCode, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }

    @GetMapping("/dutyPostSynergism")
    @ApiOperation("值班岗位协同")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orgCode", value = "监所编码，不传默认当前登录用户监所编码", required = false),
            @ApiImplicitParam(name = "dutyDate", dataType = "String", value = "不传默认当天", required = false)
    })
    public CommonResult<List<PostSynergismRespVO>> dutyPostSynergism(@RequestParam(required = false) String orgCode,
                                                                     @RequestParam(required = false) String dutyDate) {
        try {
            //如果日期为空则获取当前日期
            if (StringUtil.isEmpty(dutyDate)) {
                dutyDate = DateUtil.getCurrentDate();
            }
            if (StringUtil.isEmpty(orgCode)) {
                orgCode = SessionUserUtil.getSessionUser().getOrgCode();
            }
            return CommonResult.success(staffDutyRecordService.dutyPostSynergism(dutyDate,orgCode, StaffDutyTypeEnum.ON_DUTY_AT_INSTITUTE.getCode()));
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
}
